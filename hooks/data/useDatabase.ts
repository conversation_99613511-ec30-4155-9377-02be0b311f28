'use client'

import { User, SignupFormData, ProfileSetupData } from '@/lib/models/user'

// API endpoints
const API_BASE = '/api/v1'

// User operations
export const userOperations = {
  async createUser(userData: SignupFormData): Promise<{ success: boolean; userId?: string; error?: string }> {
    try {
      const response = await fetch(`${API_BASE}/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData),
      })
      
      const result = await response.json()
      return result
    } catch (error) {
      console.error('Create user error:', error)
      return { success: false, error: 'Network error' }
    }
  },

  async verifyEmail(email: string, code: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`${API_BASE}/auth/verify-email`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, code }),
      })
      
      const result = await response.json()
      return result
    } catch (error) {
      console.error('Verify email error:', error)
      return { success: false, error: 'Network error' }
    }
  },

  async completeProfile(profileData: ProfileSetupData): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`${API_BASE}/auth/complete-profile`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(profileData),
      })
      
      const result = await response.json()
      return result
    } catch (error) {
      console.error('Complete profile error:', error)
      return { success: false, error: 'Network error' }
    }
  },

  async login(emailOrUsername: string, password: string): Promise<{ success: boolean; requiresVerification?: boolean; error?: string }> {
    try {
      const response = await fetch(`${API_BASE}/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ emailOrUsername, password }),
      })
      
      const result = await response.json()
      return result
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, error: 'Network error' }
    }
  },

  async resendVerificationCode(email: string): Promise<{ success: boolean; error?: string; retryAfter?: number }> {
    try {
      const response = await fetch(`${API_BASE}/auth/resend-code`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      })
      
      const result = await response.json()
      return result
    } catch (error) {
      console.error('Resend code error:', error)
      return { success: false, error: 'Network error' }
    }
  },

  async checkUsernameAvailability(username: string): Promise<{ available: boolean; error?: string }> {
    try {
      const response = await fetch(`${API_BASE}/auth/check-username?username=${encodeURIComponent(username)}`)
      const result = await response.json()
      return result.success ? result.data : { available: false, error: result.error }
    } catch (error) {
      console.error('Check username error:', error)
      return { available: false, error: 'Network error' }
    }
  },

  async getCurrentUser(): Promise<{ user?: Omit<User, 'passwordHash'>; error?: string }> {
    try {
      const response = await fetch(`${API_BASE}/auth/me`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        return { error: 'Not authenticated' }
      }
      
      const result = await response.json()
      return result
    } catch (error) {
      console.error('Get current user error:', error)
      return { error: 'Network error' }
    }
  },

  async logout(): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`${API_BASE}/auth/logout`, {
        method: 'POST',
        credentials: 'include'
      })
      
      const result = await response.json()
      return result
    } catch (error) {
      console.error('Logout error:', error)
      return { success: false, error: 'Network error' }
    }
  }
}

// Mail operations
export const mailOperations = {
  async sendVerificationEmail(email: string, type: 'signup' | 'login' = 'signup'): Promise<{ success: boolean; error?: string; retryAfter?: number }> {
    try {
      const response = await fetch(`${API_BASE}/mail/send-verification`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, type }),
      })
      
      const result = await response.json()
      return result
    } catch (error) {
      console.error('Send verification email error:', error)
      return { success: false, error: 'Network error' }
    }
  }
}

// Validation helpers
export const validationHelpers = {
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email) && email.length <= 255
  },

  isValidUsername(username: string): boolean {
    const usernameRegex = /^[a-zA-Z0-9_-]+$/
    return usernameRegex.test(username) && 
           username.length >= 3 && 
           username.length <= 30 &&
           !username.startsWith('_') && 
           !username.endsWith('_')
  },

  isValidPassword(password: string): boolean {
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
    return passwordRegex.test(password) && password.length >= 8 && password.length <= 128
  },

  isValidName(name: string): boolean {
    const nameRegex = /^[a-zA-Z\s]+$/
    return nameRegex.test(name) && name.length >= 2 && name.length <= 50
  },

  isValidVerificationCode(code: string): boolean {
    return /^\d{6}$/.test(code)
  },

  isValidGithubUsername(username: string): boolean {
    if (!username) return true // Optional field
    const githubRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-])*[a-zA-Z0-9]$|^[a-zA-Z0-9]$/
    return githubRegex.test(username) && username.length <= 39
  },

  isValidDiscordUsername(username: string): boolean {
    if (!username) return true // Optional field
    const discordRegex = /^[a-zA-Z0-9._]+$/
    return discordRegex.test(username) && username.length <= 32
  }
}

// Error handling
export class DatabaseError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 400
  ) {
    super(message)
    this.name = 'DatabaseError'
  }
}

export const DatabaseErrorCodes = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  SERVER_ERROR: 'SERVER_ERROR'
} as const

// Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  code?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Local storage helpers
export const storageHelpers = {
  setItem(key: string, value: any): void {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('Failed to save to localStorage:', error)
    }
  },

  getItem<T>(key: string): T | null {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : null
    } catch (error) {
      console.error('Failed to read from localStorage:', error)
      return null
    }
  },

  removeItem(key: string): void {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('Failed to remove from localStorage:', error)
    }
  },

  clear(): void {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('Failed to clear localStorage:', error)
    }
  }
}

// Session storage helpers
export const sessionHelpers = {
  setItem(key: string, value: any): void {
    try {
      sessionStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('Failed to save to sessionStorage:', error)
    }
  },

  getItem<T>(key: string): T | null {
    try {
      const item = sessionStorage.getItem(key)
      return item ? JSON.parse(item) : null
    } catch (error) {
      console.error('Failed to read from sessionStorage:', error)
      return null
    }
  },

  removeItem(key: string): void {
    try {
      sessionStorage.removeItem(key)
    } catch (error) {
      console.error('Failed to remove from sessionStorage:', error)
    }
  }
}
