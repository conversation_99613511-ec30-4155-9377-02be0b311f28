'use client'

import { useState, useEffect, useCallback, createContext, useContext } from 'react'
import { User } from '@/lib/models/user'
import { userOperations, storageHelpers } from '@/hooks/data/useDatabase'

// Auth context types
interface AuthContextType {
  user: Omit<User, 'passwordHash'> | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (emailOrUsername: string, password: string) => Promise<LoginResult>
  logout: () => Promise<void>
  logoutAllDevices: () => Promise<void>
  refreshUser: () => Promise<void>
  updatePreferences: (preferences: Partial<User['preferences']>) => Promise<boolean>
}

interface LoginResult {
  success: boolean
  requiresVerification?: boolean
  error?: string
  redirectTo?: string
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Auth provider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<Omit<User, 'passwordHash'> | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  // Initialize auth state
  useEffect(() => {
    initializeAuth()
  }, [])

  const initializeAuth = useCallback(async () => {
    try {
      setIsLoading(true)
      
      // Try to get user from API
      const result = await userOperations.getCurrentUser()
      
      if (result.user) {
        setUser(result.user)
        setIsAuthenticated(true)
        
        // Cache user data
        storageHelpers.setItem('user', result.user)
      } else {
        // Clear any cached data
        setUser(null)
        setIsAuthenticated(false)
        storageHelpers.removeItem('user')
      }
    } catch (error) {
      console.error('Auth initialization error:', error)
      setUser(null)
      setIsAuthenticated(false)
      storageHelpers.removeItem('user')
    } finally {
      setIsLoading(false)
    }
  }, [])

  const login = useCallback(async (emailOrUsername: string, password: string): Promise<LoginResult> => {
    try {
      const result = await userOperations.login(emailOrUsername, password)
      
      if (result.success && !result.requiresVerification) {
        // Login successful, refresh user data
        await refreshUser()
        return { success: true, redirectTo: '/home' }
      }
      
      return result
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, error: 'Network error occurred' }
    }
  }, [])

  const logout = useCallback(async () => {
    try {
      await userOperations.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Always clear local state
      setUser(null)
      setIsAuthenticated(false)
      storageHelpers.removeItem('user')
    }
  }, [])

  const logoutAllDevices = useCallback(async () => {
    try {
      const response = await fetch('/api/v1/auth/logout', {
        method: 'DELETE',
        credentials: 'include'
      })
      
      if (!response.ok) {
        throw new Error('Failed to logout from all devices')
      }
    } catch (error) {
      console.error('Logout all devices error:', error)
      throw error
    } finally {
      // Always clear local state
      setUser(null)
      setIsAuthenticated(false)
      storageHelpers.removeItem('user')
    }
  }, [])

  const refreshUser = useCallback(async () => {
    try {
      const result = await userOperations.getCurrentUser()
      
      if (result.user) {
        setUser(result.user)
        setIsAuthenticated(true)
        storageHelpers.setItem('user', result.user)
      } else {
        setUser(null)
        setIsAuthenticated(false)
        storageHelpers.removeItem('user')
      }
    } catch (error) {
      console.error('Refresh user error:', error)
      setUser(null)
      setIsAuthenticated(false)
      storageHelpers.removeItem('user')
    }
  }, [])

  const updatePreferences = useCallback(async (preferences: Partial<User['preferences']>): Promise<boolean> => {
    try {
      const response = await fetch('/api/v1/auth/me', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ preferences })
      })
      
      const result = await response.json()
      
      if (result.success && result.data.user) {
        setUser(result.data.user)
        storageHelpers.setItem('user', result.data.user)
        return true
      }
      
      return false
    } catch (error) {
      console.error('Update preferences error:', error)
      return false
    }
  }, [])

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    logoutAllDevices,
    refreshUser,
    updatePreferences
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Auth guard hook
export function useAuthGuard(redirectTo: string = '/auth/signin') {
  const { isAuthenticated, isLoading, user } = useAuth()
  const [shouldRedirect, setShouldRedirect] = useState(false)

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        setShouldRedirect(true)
      } else if (user && !user.isProfileComplete && !window.location.pathname.includes('/auth/complete-profile')) {
        // Redirect to profile completion if not complete
        window.location.href = '/auth/complete-profile'
      }
    }
  }, [isAuthenticated, isLoading, user])

  useEffect(() => {
    if (shouldRedirect) {
      window.location.href = redirectTo
    }
  }, [shouldRedirect, redirectTo])

  return {
    isAuthenticated,
    isLoading,
    user,
    shouldRedirect
  }
}

// Profile completion guard
export function useProfileGuard() {
  const { user, isLoading, isAuthenticated } = useAuth()
  const [needsProfileCompletion, setNeedsProfileCompletion] = useState(false)

  useEffect(() => {
    if (!isLoading && isAuthenticated && user) {
      if (!user.isProfileComplete) {
        setNeedsProfileCompletion(true)
      }
    }
  }, [isLoading, isAuthenticated, user])

  return {
    needsProfileCompletion,
    isLoading,
    user
  }
}

// Session management hook
export function useSession() {
  const { user, isAuthenticated, isLoading, refreshUser } = useAuth()
  const [lastActivity, setLastActivity] = useState(Date.now())

  // Update last activity on user interaction
  useEffect(() => {
    const updateActivity = () => {
      setLastActivity(Date.now())
    }

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
    events.forEach(event => {
      document.addEventListener(event, updateActivity, true)
    })

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, updateActivity, true)
      })
    }
  }, [])

  // Refresh user data periodically if active
  useEffect(() => {
    if (!isAuthenticated || isLoading) return

    const interval = setInterval(() => {
      const timeSinceActivity = Date.now() - lastActivity
      
      // Only refresh if user has been active in the last 5 minutes
      if (timeSinceActivity < 5 * 60 * 1000) {
        refreshUser()
      }
    }, 10 * 60 * 1000) // Check every 10 minutes

    return () => clearInterval(interval)
  }, [isAuthenticated, isLoading, lastActivity, refreshUser])

  return {
    user,
    isAuthenticated,
    isLoading,
    lastActivity
  }
}
