'use client'

import { useState, useCallback, useEffect, useRef } from 'react'
import { userOperations, mailOperations, validationHelpers } from '@/hooks/data/useDatabase'

interface VerificationState {
  email: string
  code: string
  isLoading: boolean
  isVerifying: boolean
  isResending: boolean
  error: string | null
  success: boolean
  verified: boolean
  canResend: boolean
  retryAfter: number
  attemptsRemaining: number
}

interface VerificationResult {
  success: boolean
  error?: string
  verified?: boolean
  nextStep?: string
}

interface ResendResult {
  success: boolean
  error?: string
  retryAfter?: number
  emailSent?: boolean
}

export function useEmailVerification(initialEmail: string = '', type: 'signup' | 'login' = 'signup') {
  const [state, setState] = useState<VerificationState>({
    email: initialEmail.toLowerCase(),
    code: '',
    isLoading: false,
    isVerifying: false,
    isResending: false,
    error: null,
    success: false,
    verified: false,
    canResend: false,
    retryAfter: 0,
    attemptsRemaining: 5
  })

  const retryTimerRef = useRef<NodeJS.Timeout | null>(null)
  const resendTimerRef = useRef<NodeJS.Timeout | null>(null)

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      if (retryTimerRef.current) {
        clearInterval(retryTimerRef.current)
      }
      if (resendTimerRef.current) {
        clearInterval(resendTimerRef.current)
      }
    }
  }, [])

  // Start resend cooldown timer
  const startResendCooldown = useCallback((seconds: number = 60) => {
    setState(prev => ({ ...prev, canResend: false, retryAfter: seconds }))

    if (resendTimerRef.current) {
      clearInterval(resendTimerRef.current)
    }

    resendTimerRef.current = setInterval(() => {
      setState(prev => {
        const newRetryAfter = prev.retryAfter - 1
        if (newRetryAfter <= 0) {
          if (resendTimerRef.current) {
            clearInterval(resendTimerRef.current)
          }
          return { ...prev, canResend: true, retryAfter: 0 }
        }
        return { ...prev, retryAfter: newRetryAfter }
      })
    }, 1000)
  }, [])

  // Set email
  const setEmail = useCallback((email: string) => {
    setState(prev => ({
      ...prev,
      email: email.toLowerCase(),
      error: null,
      verified: false,
      success: false
    }))
  }, [])

  // Set verification code
  const setCode = useCallback((code: string) => {
    // Only allow digits and limit to 6 characters
    const cleanCode = code.replace(/\D/g, '').slice(0, 6)
    setState(prev => ({ ...prev, code: cleanCode, error: null }))
  }, [])

  // Verify email with code
  const verifyCode = useCallback(async (): Promise<VerificationResult> => {
    if (!state.email) {
      return { success: false, error: 'Email is required' }
    }

    if (!validationHelpers.isValidVerificationCode(state.code)) {
      setState(prev => ({ ...prev, error: 'Please enter a valid 6-digit code' }))
      return { success: false, error: 'Invalid code format' }
    }

    setState(prev => ({ ...prev, isVerifying: true, error: null }))

    try {
      const result = await userOperations.verifyEmail(state.email, state.code)

      if (result.success) {
        setState(prev => ({
          ...prev,
          isVerifying: false,
          success: true,
          verified: true,
          error: null
        }))
        return { 
          success: true, 
          verified: true, 
          nextStep: type === 'signup' ? 'profile_setup' : 'home' 
        }
      } else {
        // Handle specific error cases
        let attemptsRemaining = state.attemptsRemaining - 1
        
        if (result.error?.includes('attempts remaining')) {
          const match = result.error.match(/(\d+) attempts remaining/)
          if (match) {
            attemptsRemaining = parseInt(match[1])
          }
        }

        setState(prev => ({
          ...prev,
          isVerifying: false,
          error: result.error || 'Verification failed',
          attemptsRemaining,
          code: '' // Clear the code on error
        }))
        
        return { success: false, error: result.error }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Verification failed'
      setState(prev => ({
        ...prev,
        isVerifying: false,
        error: errorMessage,
        code: ''
      }))
      return { success: false, error: errorMessage }
    }
  }, [state.email, state.code, state.attemptsRemaining, type])

  // Resend verification code
  const resendCode = useCallback(async (): Promise<ResendResult> => {
    if (!state.email) {
      return { success: false, error: 'Email is required' }
    }

    if (!state.canResend && state.retryAfter > 0) {
      return { 
        success: false, 
        error: `Please wait ${state.retryAfter} seconds before requesting a new code`,
        retryAfter: state.retryAfter 
      }
    }

    setState(prev => ({ ...prev, isResending: true, error: null }))

    try {
      const result = await mailOperations.sendVerificationEmail(state.email, type)

      if (result.success) {
        setState(prev => ({
          ...prev,
          isResending: false,
          success: false, // Reset success state for new code
          verified: false,
          code: '', // Clear current code
          attemptsRemaining: 5, // Reset attempts
          error: null
        }))

        // Start cooldown timer
        startResendCooldown(result.retryAfter || 60)

        return { 
          success: true, 
          emailSent: true,
          retryAfter: result.retryAfter || 60
        }
      } else {
        setState(prev => ({
          ...prev,
          isResending: false,
          error: result.error || 'Failed to resend code'
        }))

        // If rate limited, start cooldown
        if (result.retryAfter) {
          startResendCooldown(result.retryAfter)
        }

        return { success: false, error: result.error, retryAfter: result.retryAfter }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to resend code'
      setState(prev => ({
        ...prev,
        isResending: false,
        error: errorMessage
      }))
      return { success: false, error: errorMessage }
    }
  }, [state.email, state.canResend, state.retryAfter, type, startResendCooldown])

  // Auto-verify when code is complete
  const autoVerify = useCallback(async () => {
    if (state.code.length === 6 && !state.isVerifying && !state.verified) {
      await verifyCode()
    }
  }, [state.code, state.isVerifying, state.verified, verifyCode])

  // Effect to auto-verify when code is complete
  useEffect(() => {
    if (state.code.length === 6) {
      const timer = setTimeout(autoVerify, 500) // Small delay to prevent rapid API calls
      return () => clearTimeout(timer)
    }
  }, [state.code, autoVerify])

  // Initialize resend cooldown on mount if email is provided
  useEffect(() => {
    if (initialEmail && type === 'signup') {
      startResendCooldown(60)
    }
  }, [initialEmail, type, startResendCooldown])

  // Reset state
  const reset = useCallback(() => {
    setState({
      email: '',
      code: '',
      isLoading: false,
      isVerifying: false,
      isResending: false,
      error: null,
      success: false,
      verified: false,
      canResend: true,
      retryAfter: 0,
      attemptsRemaining: 5
    })

    if (retryTimerRef.current) {
      clearInterval(retryTimerRef.current)
    }
    if (resendTimerRef.current) {
      clearInterval(resendTimerRef.current)
    }
  }, [])

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  return {
    // State
    email: state.email,
    code: state.code,
    isLoading: state.isLoading,
    isVerifying: state.isVerifying,
    isResending: state.isResending,
    error: state.error,
    success: state.success,
    verified: state.verified,
    canResend: state.canResend,
    retryAfter: state.retryAfter,
    attemptsRemaining: state.attemptsRemaining,

    // Actions
    setEmail,
    setCode,
    verifyCode,
    resendCode,
    reset,
    clearError,

    // Computed values
    isCodeComplete: state.code.length === 6,
    canVerify: state.code.length === 6 && !state.isVerifying && !state.verified,
    hasError: !!state.error,
    isProcessing: state.isVerifying || state.isResending,
    
    // Format helpers
    formatRetryTime: () => {
      const minutes = Math.floor(state.retryAfter / 60)
      const seconds = state.retryAfter % 60
      return minutes > 0 ? `${minutes}:${seconds.toString().padStart(2, '0')}` : `${seconds}s`
    }
  }
}
