'use client'

import { useState, useCallback } from 'react'
import { SignupFormData, ProfileSetupData } from '@/lib/models/user'
import { userOperations, validationHelpers } from '@/hooks/data/useDatabase'

// Signup flow states
export type SignupStep = 'registration' | 'email_verification' | 'profile_setup' | 'complete'

interface SignupState {
  step: SignupStep
  email: string
  userId?: string
  isLoading: boolean
  error: string | null
  success: boolean
}

interface SignupResult {
  success: boolean
  error?: string
  userId?: string
  emailSent?: boolean
}

interface VerificationResult {
  success: boolean
  error?: string
  verified?: boolean
  nextStep?: string
}

interface ProfileResult {
  success: boolean
  error?: string
  profileComplete?: boolean
  redirectTo?: string
}

export function useSignup() {
  const [state, setState] = useState<SignupState>({
    step: 'registration',
    email: '',
    isLoading: false,
    error: null,
    success: false
  })

  // Username availability checking
  const [usernameCheck, setUsernameCheck] = useState<{
    username: string
    available: boolean | null
    isChecking: boolean
  }>({
    username: '',
    available: null,
    isChecking: false
  })

  // Reset state
  const resetSignup = useCallback(() => {
    setState({
      step: 'registration',
      email: '',
      isLoading: false,
      error: null,
      success: false
    })
    setUsernameCheck({
      username: '',
      available: null,
      isChecking: false
    })
  }, [])

  // Check username availability
  const checkUsername = useCallback(async (username: string): Promise<boolean> => {
    if (!username || username.length < 3) {
      setUsernameCheck({ username, available: null, isChecking: false })
      return false
    }

    if (!validationHelpers.isValidUsername(username)) {
      setUsernameCheck({ username, available: false, isChecking: false })
      return false
    }

    setUsernameCheck({ username, available: null, isChecking: true })

    try {
      const result = await userOperations.checkUsernameAvailability(username)
      setUsernameCheck({ 
        username, 
        available: result.available, 
        isChecking: false 
      })
      return result.available
    } catch (error) {
      console.error('Username check error:', error)
      setUsernameCheck({ username, available: false, isChecking: false })
      return false
    }
  }, [])

  // Register user
  const register = useCallback(async (formData: SignupFormData): Promise<SignupResult> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      // Client-side validation
      if (!validationHelpers.isValidEmail(formData.email)) {
        throw new Error('Invalid email format')
      }

      if (!validationHelpers.isValidUsername(formData.username)) {
        throw new Error('Invalid username format')
      }

      if (!validationHelpers.isValidPassword(formData.password)) {
        throw new Error('Password does not meet requirements')
      }

      if (!validationHelpers.isValidName(formData.firstName)) {
        throw new Error('Invalid first name')
      }

      if (!validationHelpers.isValidName(formData.lastName)) {
        throw new Error('Invalid last name')
      }

      if (formData.password !== formData.confirmPassword) {
        throw new Error('Passwords do not match')
      }

      // Check username availability one more time
      const isUsernameAvailable = await checkUsername(formData.username)
      if (!isUsernameAvailable) {
        throw new Error('Username is not available')
      }

      // Register user
      const result = await userOperations.createUser(formData)

      if (result.success) {
        setState(prev => ({
          ...prev,
          step: 'email_verification',
          email: formData.email.toLowerCase(),
          userId: result.userId,
          isLoading: false,
          success: true
        }))
        return { success: true, userId: result.userId, emailSent: true }
      } else {
        setState(prev => ({ ...prev, error: result.error || 'Registration failed', isLoading: false }))
        return { success: false, error: result.error }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration failed'
      setState(prev => ({ ...prev, error: errorMessage, isLoading: false }))
      return { success: false, error: errorMessage }
    }
  }, [checkUsername])

  // Verify email
  const verifyEmail = useCallback(async (code: string): Promise<VerificationResult> => {
    if (!state.email) {
      return { success: false, error: 'No email found in signup state' }
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      if (!validationHelpers.isValidVerificationCode(code)) {
        throw new Error('Invalid verification code format')
      }

      const result = await userOperations.verifyEmail(state.email, code)

      if (result.success) {
        setState(prev => ({
          ...prev,
          step: 'profile_setup',
          isLoading: false,
          success: true
        }))
        return { success: true, verified: true, nextStep: 'profile_setup' }
      } else {
        setState(prev => ({ ...prev, error: result.error || 'Verification failed', isLoading: false }))
        return { success: false, error: result.error }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Verification failed'
      setState(prev => ({ ...prev, error: errorMessage, isLoading: false }))
      return { success: false, error: errorMessage }
    }
  }, [state.email])

  // Complete profile
  const completeProfile = useCallback(async (profileData: ProfileSetupData): Promise<ProfileResult> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      // Client-side validation
      if (profileData.githubUsername && !validationHelpers.isValidGithubUsername(profileData.githubUsername)) {
        throw new Error('Invalid GitHub username format')
      }

      if (profileData.discordUsername && !validationHelpers.isValidDiscordUsername(profileData.discordUsername)) {
        throw new Error('Invalid Discord username format')
      }

      if (!profileData.description || profileData.description.trim().length < 10) {
        throw new Error('Description must be at least 10 characters')
      }

      if (!profileData.position) {
        throw new Error('Position is required')
      }

      const result = await userOperations.completeProfile(profileData)

      if (result.success) {
        setState(prev => ({
          ...prev,
          step: 'complete',
          isLoading: false,
          success: true
        }))
        return { 
          success: true, 
          profileComplete: true, 
          redirectTo: '/home' 
        }
      } else {
        setState(prev => ({ ...prev, error: result.error || 'Profile completion failed', isLoading: false }))
        return { success: false, error: result.error }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Profile completion failed'
      setState(prev => ({ ...prev, error: errorMessage, isLoading: false }))
      return { success: false, error: errorMessage }
    }
  }, [])

  // Resend verification code
  const resendVerificationCode = useCallback(async (): Promise<{ success: boolean; error?: string; retryAfter?: number }> => {
    if (!state.email) {
      return { success: false, error: 'No email found in signup state' }
    }

    try {
      const result = await userOperations.resendVerificationCode(state.email)
      return result
    } catch (error) {
      console.error('Resend verification code error:', error)
      return { success: false, error: 'Failed to resend verification code' }
    }
  }, [state.email])

  // Go to specific step
  const goToStep = useCallback((step: SignupStep) => {
    setState(prev => ({ ...prev, step, error: null }))
  }, [])

  // Set email for verification step
  const setEmailForVerification = useCallback((email: string) => {
    setState(prev => ({
      ...prev,
      email: email.toLowerCase(),
      step: 'email_verification',
      error: null
    }))
  }, [])

  return {
    // State
    state,
    usernameCheck,
    
    // Actions
    register,
    verifyEmail,
    completeProfile,
    checkUsername,
    resendVerificationCode,
    resetSignup,
    goToStep,
    setEmailForVerification,
    
    // Computed values
    canProceed: !state.isLoading && !state.error,
    isRegistrationStep: state.step === 'registration',
    isVerificationStep: state.step === 'email_verification',
    isProfileStep: state.step === 'profile_setup',
    isComplete: state.step === 'complete'
  }
}
