'use client'

import { useState, useCallback } from 'react'

interface MailState {
  isLoading: boolean
  error: string | null
  success: boolean
  lastSentAt: number | null
  retryAfter: number
}

interface SendResult {
  success: boolean
  error?: string
  retryAfter?: number
  emailSent?: boolean
}

interface ContactFormData {
  name: string
  email: string
  subject: string
  message: string
}

interface NewsletterData {
  email: string
  preferences?: {
    weeklyDigest?: boolean
    projectUpdates?: boolean
    communityNews?: boolean
  }
}

export function useMailService() {
  const [state, setState] = useState<MailState>({
    isLoading: false,
    error: null,
    success: false,
    lastSentAt: null,
    retryAfter: 0
  })

  // Send verification email
  const sendVerificationEmail = useCallback(async (
    email: string, 
    type: 'signup' | 'login' = 'signup'
  ): Promise<SendResult> => {
    // Check rate limiting
    if (state.lastSentAt && Date.now() - state.lastSentAt < 60000) {
      const retryAfter = Math.ceil((60000 - (Date.now() - state.lastSentAt)) / 1000)
      return {
        success: false,
        error: `Please wait ${retryAfter} seconds before requesting another email`,
        retryAfter
      }
    }

    setState(prev => ({ ...prev, isLoading: true, error: null, success: false }))

    try {
      const response = await fetch('/api/v1/mail/send-verification', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, type })
      })

      const result = await response.json()

      if (result.success) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          success: true,
          lastSentAt: Date.now(),
          retryAfter: result.data?.retryAfter || 60
        }))
        
        return {
          success: true,
          emailSent: result.data?.emailSent || true,
          retryAfter: result.data?.retryAfter || 60
        }
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || 'Failed to send email',
          retryAfter: result.data?.retryAfter || 0
        }))
        
        return {
          success: false,
          error: result.error,
          retryAfter: result.data?.retryAfter
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Network error'
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))
      
      return { success: false, error: errorMessage }
    }
  }, [state.lastSentAt])

  // Send contact form email
  const sendContactEmail = useCallback(async (formData: ContactFormData): Promise<SendResult> => {
    setState(prev => ({ ...prev, isLoading: true, error: null, success: false }))

    try {
      // Validate form data
      if (!formData.name?.trim()) {
        throw new Error('Name is required')
      }
      if (!formData.email?.trim()) {
        throw new Error('Email is required')
      }
      if (!formData.subject?.trim()) {
        throw new Error('Subject is required')
      }
      if (!formData.message?.trim()) {
        throw new Error('Message is required')
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(formData.email)) {
        throw new Error('Invalid email format')
      }

      const response = await fetch('/api/v1/mail/contact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      const result = await response.json()

      if (result.success) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          success: true,
          lastSentAt: Date.now()
        }))
        
        return { success: true, emailSent: true }
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || 'Failed to send message'
        }))
        
        return { success: false, error: result.error }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send message'
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))
      
      return { success: false, error: errorMessage }
    }
  }, [])

  // Subscribe to newsletter
  const subscribeNewsletter = useCallback(async (data: NewsletterData): Promise<SendResult> => {
    setState(prev => ({ ...prev, isLoading: true, error: null, success: false }))

    try {
      // Validate email
      if (!data.email?.trim()) {
        throw new Error('Email is required')
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(data.email)) {
        throw new Error('Invalid email format')
      }

      const response = await fetch('/api/v1/mail/newsletter', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      const result = await response.json()

      if (result.success) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          success: true,
          lastSentAt: Date.now()
        }))
        
        return { success: true, emailSent: true }
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || 'Failed to subscribe'
        }))
        
        return { success: false, error: result.error }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to subscribe'
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))
      
      return { success: false, error: errorMessage }
    }
  }, [])

  // Send notification email (admin only)
  const sendNotificationEmail = useCallback(async (
    recipients: string[],
    subject: string,
    message: string,
    type: 'announcement' | 'update' | 'alert' = 'announcement'
  ): Promise<SendResult> => {
    setState(prev => ({ ...prev, isLoading: true, error: null, success: false }))

    try {
      if (!recipients?.length) {
        throw new Error('Recipients are required')
      }
      if (!subject?.trim()) {
        throw new Error('Subject is required')
      }
      if (!message?.trim()) {
        throw new Error('Message is required')
      }

      const response = await fetch('/api/v1/mail/notification', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ recipients, subject, message, type })
      })

      const result = await response.json()

      if (result.success) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          success: true,
          lastSentAt: Date.now()
        }))
        
        return { success: true, emailSent: true }
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || 'Failed to send notification'
        }))
        
        return { success: false, error: result.error }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send notification'
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))
      
      return { success: false, error: errorMessage }
    }
  }, [])

  // Reset state
  const reset = useCallback(() => {
    setState({
      isLoading: false,
      error: null,
      success: false,
      lastSentAt: null,
      retryAfter: 0
    })
  }, [])

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  // Check if can send (rate limiting)
  const canSend = useCallback(() => {
    if (!state.lastSentAt) return true
    return Date.now() - state.lastSentAt >= 60000 // 1 minute cooldown
  }, [state.lastSentAt])

  // Get time until can send again
  const getRetryTime = useCallback(() => {
    if (!state.lastSentAt) return 0
    const elapsed = Date.now() - state.lastSentAt
    return Math.max(0, Math.ceil((60000 - elapsed) / 1000))
  }, [state.lastSentAt])

  return {
    // State
    isLoading: state.isLoading,
    error: state.error,
    success: state.success,
    retryAfter: state.retryAfter,
    
    // Actions
    sendVerificationEmail,
    sendContactEmail,
    subscribeNewsletter,
    sendNotificationEmail,
    reset,
    clearError,
    
    // Computed values
    canSend: canSend(),
    retryTime: getRetryTime(),
    hasError: !!state.error,
    
    // Validation helpers
    validateEmail: (email: string) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return emailRegex.test(email) && email.length <= 255
    },
    
    validateContactForm: (formData: ContactFormData) => {
      const errors: string[] = []
      
      if (!formData.name?.trim()) errors.push('Name is required')
      if (!formData.email?.trim()) errors.push('Email is required')
      if (!formData.subject?.trim()) errors.push('Subject is required')
      if (!formData.message?.trim()) errors.push('Message is required')
      
      if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        errors.push('Invalid email format')
      }
      
      if (formData.name && formData.name.length > 100) {
        errors.push('Name must be less than 100 characters')
      }
      
      if (formData.subject && formData.subject.length > 200) {
        errors.push('Subject must be less than 200 characters')
      }
      
      if (formData.message && formData.message.length > 2000) {
        errors.push('Message must be less than 2000 characters')
      }
      
      return {
        isValid: errors.length === 0,
        errors
      }
    }
  }
}
