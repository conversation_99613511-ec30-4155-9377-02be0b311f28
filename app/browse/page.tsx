"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import ModernProjectCard from "@/components/modern-project-card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { projects, searchProjects, type Project } from "@/lib/data"
import { Search, Filter, X, Sparkles, Grid3X3, List } from "lucide-react"

export default function BrowsePage() {
  const searchParams = useSearchParams()
  const [filteredProjects, setFilteredProjects] = useState<Project[]>(projects)
  const [searchQuery, setSearchQuery] = useState(searchParams.get("search") || "")
  const [selectedTag, setSelectedTag] = useState(searchParams.get("tag") || "")
  const [showFilters, setShowFilters] = useState(false)
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  const allTags = Array.from(new Set(projects.flatMap((project) => project.tags)))

  useEffect(() => {
    let result = projects

    if (searchQuery) {
      result = searchProjects(searchQuery)
    }

    if (selectedTag) {
      result = result.filter((project) => project.tags.some((tag) => tag.toLowerCase() === selectedTag.toLowerCase()))
    }

    setFilteredProjects(result)
  }, [searchQuery, selectedTag])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
  }

  const clearFilters = () => {
    setSearchQuery("")
    setSelectedTag("")
  }

  return (
    <div className="min-h-screen bg-dark-950 py-12 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 right-10 w-96 h-96 bg-emerald-500/5 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 left-10 w-80 h-80 bg-violet-500/5 rounded-full blur-3xl animate-float-slow"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-12 animate-slide-up">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-dark-800/50 border border-emerald-500/20 text-emerald-400 text-sm font-medium mb-6">
            <Sparkles className="h-4 w-4 mr-2 animate-pulse" />
            Project Discovery Hub
          </div>
          <h1 className="text-4xl font-bold text-white mb-4">Browse Projects</h1>
          <p className="text-gray-400 text-lg">
            Discover {projects.length} amazing open-source projects from our community
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-12 space-y-6 animate-slide-up" style={{ animationDelay: "0.2s" }}>
          {/* Search Bar */}
          <form onSubmit={handleSearch} className="relative group">
            <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/10 to-violet-500/10 rounded-2xl blur group-focus-within:blur-md transition-all duration-300"></div>
            <div className="relative flex gap-4 glass-effect rounded-2xl p-4">
              <div className="relative flex-1">
                <Input
                  type="text"
                  placeholder="Search projects, developers, or technologies..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full input-modern pl-12 h-12 text-lg"
                />
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 group-focus-within:text-emerald-400 transition-colors duration-300" />
              </div>

              <Button
                type="button"
                onClick={() => setShowFilters(!showFilters)}
                className={`btn-secondary h-12 px-6 ${showFilters ? "bg-emerald-500/20 border-emerald-500/30" : ""}`}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>

              <div className="flex border border-dark-600 rounded-xl overflow-hidden">
                <button
                  type="button"
                  onClick={() => setViewMode("grid")}
                  className={`p-3 transition-colors duration-300 ${
                    viewMode === "grid"
                      ? "bg-emerald-500/20 text-emerald-400"
                      : "text-gray-400 hover:text-white hover:bg-dark-700"
                  }`}
                >
                  <Grid3X3 className="h-4 w-4" />
                </button>
                <button
                  type="button"
                  onClick={() => setViewMode("list")}
                  className={`p-3 transition-colors duration-300 ${
                    viewMode === "list"
                      ? "bg-emerald-500/20 text-emerald-400"
                      : "text-gray-400 hover:text-white hover:bg-dark-700"
                  }`}
                >
                  <List className="h-4 w-4" />
                </button>
              </div>
            </div>
          </form>

          {/* Filter Tags */}
          {showFilters && (
            <div className="glass-card rounded-2xl p-6 border border-dark-600 animate-slide-up">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-white font-semibold text-lg flex items-center">
                  <Filter className="h-5 w-5 mr-2 text-emerald-400" />
                  Filter by Tags
                </h3>
                {(searchQuery || selectedTag) && (
                  <Button onClick={clearFilters} className="btn-ghost text-sm">
                    <X className="h-4 w-4 mr-1" />
                    Clear All
                  </Button>
                )}
              </div>
              <div className="flex flex-wrap gap-3">
                {allTags.map((tag, index) => (
                  <Badge
                    key={tag}
                    className={`cursor-pointer transition-all duration-300 hover:scale-105 px-4 py-2 text-sm ${
                      selectedTag === tag
                        ? "bg-emerald-500/30 text-emerald-400 border-emerald-500/50"
                        : "bg-dark-700 text-gray-300 hover:bg-dark-600 border-dark-600"
                    }`}
                    onClick={() => setSelectedTag(selectedTag === tag ? "" : tag)}
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Active Filters */}
          {(searchQuery || selectedTag) && (
            <div className="flex items-center gap-3 text-sm animate-slide-up">
              <span className="text-gray-400">Active filters:</span>
              {searchQuery && (
                <Badge className="bg-emerald-500/20 text-emerald-400 border-emerald-500/30">
                  Search: {searchQuery}
                  <button onClick={() => setSearchQuery("")} className="ml-2 hover:text-white">
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {selectedTag && (
                <Badge className="bg-violet-500/20 text-violet-400 border-violet-500/30">
                  Tag: {selectedTag}
                  <button onClick={() => setSelectedTag("")} className="ml-2 hover:text-white">
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
            </div>
          )}
        </div>

        {/* Results */}
        <div className="mb-8 animate-slide-up" style={{ animationDelay: "0.4s" }}>
          <p className="text-gray-400 text-lg">
            Showing <span className="text-emerald-400 font-semibold">{filteredProjects.length}</span> of{" "}
            <span className="text-white font-semibold">{projects.length}</span> projects
          </p>
        </div>

        {/* Projects Grid */}
        {filteredProjects.length > 0 ? (
          <div
            className={`${viewMode === "grid" ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" : "space-y-6"} stagger-animation`}
          >
            {filteredProjects.map((project, index) => (
              <ModernProjectCard key={project.id} project={project} index={index} />
            ))}
          </div>
        ) : (
          <div className="text-center py-20 animate-slide-up">
            <div className="glass-card rounded-2xl p-12 max-w-md mx-auto">
              <div className="w-20 h-20 bg-dark-700 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Search className="h-10 w-10 text-gray-400" />
              </div>
              <h3 className="text-2xl font-semibold text-white mb-3">No projects found</h3>
              <p className="text-gray-400 mb-6">
                Try adjusting your search terms or filters to discover more projects.
              </p>
              <Button onClick={clearFilters} className="btn-primary">
                <Sparkles className="h-4 w-4 mr-2" />
                Clear Filters
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
