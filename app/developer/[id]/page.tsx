import { notFound } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import ProjectCard from "@/components/modern-project-card"
import { getDeveloperById, getProjectsByDeveloper } from "@/lib/data"
import { Calendar, Mail, Github, ExternalLink, Code } from "lucide-react"

interface DeveloperPageProps {
  params: {
    id: string
  }
}

export default function DeveloperPage({ params }: DeveloperPageProps) {
  const developer = getDeveloperById(params.id)
  const projects = getProjectsByDeveloper(params.id)

  if (!developer) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-gray-950 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <div className="flex items-center text-sm text-gray-400 mb-8">
          <Link href="/" className="hover:text-white transition-colors">
            Home
          </Link>
          <span className="mx-2">/</span>
          <Link href="/browse" className="hover:text-white transition-colors">
            Browse
          </Link>
          <span className="mx-2">/</span>
          <span className="text-white">{developer.name}</span>
        </div>

        {/* Developer Header */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* Developer Info */}
          <div className="lg:col-span-2">
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-8">
                <div className="flex flex-col sm:flex-row items-start space-y-4 sm:space-y-0 sm:space-x-6">
                  <Image
                    src={developer.avatar || "/placeholder.svg"}
                    alt={developer.name}
                    width={120}
                    height={120}
                    className="rounded-full mx-auto sm:mx-0"
                  />
                  <div className="flex-1 text-center sm:text-left">
                    <h1 className="text-3xl font-bold text-white mb-2">{developer.name}</h1>
                    <p className="text-gray-400 mb-4">{developer.email}</p>
                    <p className="text-gray-300 leading-relaxed mb-6">{developer.bio}</p>

                    <div className="flex flex-col sm:flex-row items-center gap-4">
                      <div className="flex items-center text-gray-400 text-sm">
                        <Calendar className="h-4 w-4 mr-2" />
                        <span>
                          Joined{" "}
                          {new Date(developer.joinDate).toLocaleDateString("en-US", {
                            year: "numeric",
                            month: "long",
                          })}
                        </span>
                      </div>

                      <div className="flex space-x-3">
                        <a
                          href={`mailto:${developer.email}`}
                          className="text-gray-400 hover:text-white transition-colors"
                        >
                          <Mail className="h-5 w-5" />
                        </a>
                        <a
                          href={developer.githubProfile}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-gray-400 hover:text-white transition-colors"
                        >
                          <Github className="h-5 w-5" />
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Stats */}
          <div className="space-y-6">
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Code className="h-4 w-4 text-blue-400" />
                    <span className="text-gray-300">Projects</span>
                  </div>
                  <span className="text-white font-semibold">{projects.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Github className="h-4 w-4 text-green-400" />
                    <span className="text-gray-300">GitHub</span>
                  </div>
                  <a
                    href={developer.githubProfile}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-400 hover:text-blue-300 transition-colors"
                  >
                    <ExternalLink className="h-4 w-4" />
                  </a>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Contact</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <a href={`mailto:${developer.email}`} className="w-full">
                  <Button
                    variant="outline"
                    className="w-full border-gray-600 text-gray-300 hover:bg-gray-700 bg-transparent"
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    Send Email
                  </Button>
                </a>
                <a href={developer.githubProfile} target="_blank" rel="noopener noreferrer" className="w-full">
                  <Button
                    variant="outline"
                    className="w-full border-gray-600 text-gray-300 hover:bg-gray-700 bg-transparent"
                  >
                    <Github className="h-4 w-4 mr-2" />
                    View GitHub
                  </Button>
                </a>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Projects Section */}
        <div>
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-white">
              Projects by {developer.name} ({projects.length})
            </h2>
          </div>

          {projects.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {projects.map((project) => (
                <ProjectCard key={project.id} project={project} />
              ))}
            </div>
          ) : (
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="text-center py-12">
                <Code className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">No Projects Yet</h3>
                <p className="text-gray-400">
                  {developer.name} {"hasn't"} published any projects yet. Check back later!
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
