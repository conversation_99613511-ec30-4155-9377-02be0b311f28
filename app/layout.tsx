import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import ModernNavbar from "@/components/modern-navbar"
import ModernFooter from "@/components/modern-footer"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "AveExplore - Discover Amazing Open Source Projects",
  description: "Explore and discover incredible open-source projects from talented developers around the world.",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.className} bg-dark-950 text-white min-h-screen flex flex-col custom-scrollbar`}>
        <ModernNavbar />
        <main className="flex-1 pt-20">{children}</main>
        <ModernFooter />
      </body>
    </html>
  )
}
