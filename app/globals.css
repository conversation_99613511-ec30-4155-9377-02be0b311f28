@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 0%;
    --foreground: 0 0% 98%;
    --card: 0 0% 4%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 4%;
    --popover-foreground: 0 0% 98%;
    --primary: 142 76% 36%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 10%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 10%;
    --muted-foreground: 0 0% 65%;
    --accent: 0 0% 10%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 10%;
    --input: 0 0% 10%;
    --ring: 142 76% 36%;
    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-dark-950 text-foreground;
    background-image: radial-gradient(circle at 20% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(251, 113, 133, 0.03) 0%, transparent 50%);
    background-attachment: fixed;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .glass-effect {
    background: rgba(10, 10, 10, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-card {
    background: rgba(17, 17, 17, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .gradient-text {
    background: linear-gradient(135deg, #10b981, #8b5cf6, #f59e0b);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease infinite;
  }

  .hover-glow {
    transition: all 0.3s ease;
  }

  .hover-glow:hover {
    box-shadow: 0 0 30px rgba(16, 185, 129, 0.3);
    transform: translateY(-2px);
  }

  .floating-element {
    animation: float 6s ease-in-out infinite;
  }

  .stagger-animation > * {
    opacity: 0;
    animation: slide-up 0.6s ease-out forwards;
  }

  .stagger-animation > *:nth-child(1) {
    animation-delay: 0.1s;
  }
  .stagger-animation > *:nth-child(2) {
    animation-delay: 0.2s;
  }
  .stagger-animation > *:nth-child(3) {
    animation-delay: 0.3s;
  }
  .stagger-animation > *:nth-child(4) {
    animation-delay: 0.4s;
  }
  .stagger-animation > *:nth-child(5) {
    animation-delay: 0.5s;
  }
  .stagger-animation > *:nth-child(6) {
    animation-delay: 0.6s;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(16, 185, 129, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(16, 185, 129, 0.5);
  }
}

/* Custom button styles */
.btn-primary {
  @apply bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700;
  @apply text-white font-medium px-6 py-3 rounded-xl;
  @apply transition-all duration-300 ease-out;
  @apply hover:shadow-lg hover:shadow-emerald-500/25 hover:-translate-y-0.5;
  @apply active:scale-95;
}

.btn-secondary {
  @apply bg-dark-800 hover:bg-dark-700 border border-dark-600 hover:border-dark-500;
  @apply text-white font-medium px-6 py-3 rounded-xl;
  @apply transition-all duration-300 ease-out;
  @apply hover:shadow-lg hover:-translate-y-0.5;
  @apply active:scale-95;
}

.btn-ghost {
  @apply bg-transparent hover:bg-dark-800/50;
  @apply text-gray-300 hover:text-white font-medium px-6 py-3 rounded-xl;
  @apply transition-all duration-300 ease-out;
  @apply hover:-translate-y-0.5;
  @apply active:scale-95;
}

/* Input styles */
.input-modern {
  @apply bg-dark-800/50 border border-dark-600 focus:border-emerald-500;
  @apply text-white placeholder-gray-400 px-4 py-3 rounded-xl;
  @apply transition-all duration-300 ease-out;
  @apply focus:ring-2 focus:ring-emerald-500/20 focus:outline-none;
  @apply backdrop-blur-sm;
}

/* Card hover effects */
.card-hover {
  @apply transition-all duration-500 ease-out;
  @apply hover:scale-105 hover:-translate-y-1;
  @apply hover:shadow-2xl hover:shadow-black/50;
}

/* Gradient borders */
.gradient-border {
  position: relative;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(139, 92, 246, 0.1));
  border-radius: 1rem;
  padding: 1px;
}

.gradient-border::before {
  content: "";
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(135deg, #10b981, #8b5cf6);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
}
