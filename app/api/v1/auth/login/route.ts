import { NextRequest } from 'next/server'
import { getUsersCollection, getVerificationCodesCollection } from '@/lib/mongodb'
import { signinSchema, createVerificationCode } from '@/lib/models/user'
import { verifyPassword, checkRateLimit, getClientIP, getUserAgent, createErrorResponse, createSuccessResponse, AuthError, AuthErrorCodes } from '@/lib/auth'
import { sendLoginVerificationEmail } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    const userAgent = getUserAgent(request)
    
    // Rate limiting - 5 attempts per 15 minutes
    if (!checkRateLimit(`login:${clientIP}`, 5, 900000)) {
      throw new AuthError('Too many login attempts. Please try again later.', AuthErrorCodes.RATE_LIMIT_EXCEEDED, 429)
    }
    
    // Parse and validate request body
    const body = await request.json()
    const validationResult = signinSchema.safeParse(body)
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
      throw new AuthError(`Validation failed: ${errors}`, AuthErrorCodes.VALIDATION_ERROR)
    }
    
    const { emailOrUsername, password } = validationResult.data
    
    // Get database collections
    const usersCollection = await getUsersCollection()
    const verificationCollection = await getVerificationCodesCollection()
    
    // Find user by email or username
    const isEmail = emailOrUsername.includes('@')
    const query = isEmail 
      ? { email: emailOrUsername.toLowerCase() }
      : { username: emailOrUsername.toLowerCase() }
    
    const user = await usersCollection.findOne(query)
    
    if (!user) {
      // Use generic message to prevent user enumeration
      throw new AuthError('Invalid credentials', AuthErrorCodes.INVALID_CREDENTIALS, 401)
    }
    
    // Verify password
    const isValidPassword = await verifyPassword(password, user.passwordHash)
    if (!isValidPassword) {
      throw new AuthError('Invalid credentials', AuthErrorCodes.INVALID_CREDENTIALS, 401)
    }
    
    // Check if email is verified
    if (!user.isVerified) {
      throw new AuthError('Please verify your email before logging in', AuthErrorCodes.EMAIL_NOT_VERIFIED)
    }
    
    // Rate limit verification code generation per user
    if (!checkRateLimit(`login-verify:${user.email}`, 3, 300000)) { // 3 codes per 5 minutes
      throw new AuthError('Too many verification codes requested. Please try again later.', AuthErrorCodes.RATE_LIMIT_EXCEEDED, 429)
    }
    
    // Clean up old verification codes for this user
    await verificationCollection.deleteMany({
      email: user.email,
      type: 'login_verification',
      expiresAt: { $lt: new Date() }
    })
    
    // Check for existing valid verification code
    const existingCode = await verificationCollection.findOne({
      email: user.email,
      type: 'login_verification',
      isUsed: false,
      expiresAt: { $gt: new Date() }
    })
    
    let verificationCode
    if (existingCode) {
      verificationCode = existingCode
    } else {
      // Create new verification code
      const verificationCodeData = createVerificationCode(user.email, 'login_verification', clientIP, userAgent)
      const result = await verificationCollection.insertOne(verificationCodeData)
      verificationCode = { ...verificationCodeData, _id: result.insertedId }
    }
    
    // Send verification email
    const emailSent = await sendLoginVerificationEmail(user.email, verificationCode.code, user.firstName)
    
    if (!emailSent) {
      console.error('Failed to send login verification email for user:', user._id)
      throw new AuthError('Failed to send verification email. Please try again.', AuthErrorCodes.INTERNAL_ERROR, 500)
    }
    
    // Update user login metadata (but don't complete login yet)
    await usersCollection.updateOne(
      { _id: user._id },
      { 
        $set: { 
          'metadata.lastActiveAt': new Date(),
          updatedAt: new Date()
        } 
      }
    )
    
    return createSuccessResponse({
      message: 'Verification code sent to your email',
      requiresVerification: true,
      email: user.email,
      emailSent: true,
      expiresIn: 15 * 60 // 15 minutes in seconds
    })
    
  } catch (error) {
    return createErrorResponse(error)
  }
}
