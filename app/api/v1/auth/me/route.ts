import { NextRequest } from 'next/server'
import { cookies } from 'next/headers'
import { getUsersCollection } from '@/lib/mongodb'
import { verifyJWT, createErrorResponse, createSuccessResponse, AuthError, AuthErrorCodes, sanitizeUser } from '@/lib/auth'
import { ObjectId } from 'mongodb'

export async function GET(request: NextRequest) {
  try {
    // Get session token from cookies
    const cookieStore = await cookies()
    const sessionToken = cookieStore.get('session')?.value
    
    if (!sessionToken) {
      throw new AuthError('Authentication required', AuthErrorCodes.UNAUTHORIZED, 401)
    }
    
    // Verify JWT token
    const tokenPayload = await verifyJWT(sessionToken)
    if (!tokenPayload) {
      throw new AuthError('Invalid session', AuthErrorCodes.INVALID_SESSION, 401)
    }
    
    // Get user from database
    const usersCollection = await getUsersCollection()
    const user = await usersCollection.findOne({ 
      _id: new ObjectId(tokenPayload.userId) 
    })
    
    if (!user) {
      throw new AuthError('User not found', AuthErrorCodes.USER_NOT_FOUND, 404)
    }
    
    // Update last active time
    await usersCollection.updateOne(
      { _id: user._id },
      { 
        $set: { 
          'metadata.lastActiveAt': new Date(),
          updatedAt: new Date()
        } 
      }
    )
    
    return createSuccessResponse({
      user: sanitizeUser(user),
      authenticated: true,
      profileComplete: user.isProfileComplete
    })
    
  } catch (error) {
    return createErrorResponse(error)
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get session token from cookies
    const cookieStore = await cookies()
    const sessionToken = cookieStore.get('session')?.value
    
    if (!sessionToken) {
      throw new AuthError('Authentication required', AuthErrorCodes.UNAUTHORIZED, 401)
    }
    
    // Verify JWT token
    const tokenPayload = await verifyJWT(sessionToken)
    if (!tokenPayload) {
      throw new AuthError('Invalid session', AuthErrorCodes.INVALID_SESSION, 401)
    }
    
    // Parse request body
    const body = await request.json()
    const { preferences } = body
    
    if (!preferences) {
      throw new AuthError('Preferences data is required', AuthErrorCodes.VALIDATION_ERROR)
    }
    
    // Validate preferences structure
    const validPreferences: any = {}
    
    if (typeof preferences.emailNotifications === 'boolean') {
      validPreferences.emailNotifications = preferences.emailNotifications
    }
    
    if (preferences.profileVisibility === 'public' || preferences.profileVisibility === 'private') {
      validPreferences.profileVisibility = preferences.profileVisibility
    }
    
    if (Object.keys(validPreferences).length === 0) {
      throw new AuthError('No valid preferences provided', AuthErrorCodes.VALIDATION_ERROR)
    }
    
    // Update user preferences
    const usersCollection = await getUsersCollection()
    const updateResult = await usersCollection.updateOne(
      { _id: new ObjectId(tokenPayload.userId) },
      { 
        $set: { 
          preferences: validPreferences,
          updatedAt: new Date()
        } 
      }
    )
    
    if (updateResult.matchedCount === 0) {
      throw new AuthError('User not found', AuthErrorCodes.USER_NOT_FOUND, 404)
    }
    
    // Get updated user
    const updatedUser = await usersCollection.findOne({ 
      _id: new ObjectId(tokenPayload.userId) 
    })
    
    if (!updatedUser) {
      throw new AuthError('Failed to retrieve updated user', AuthErrorCodes.INTERNAL_ERROR, 500)
    }
    
    return createSuccessResponse({
      message: 'Preferences updated successfully',
      user: sanitizeUser(updatedUser)
    })
    
  } catch (error) {
    return createErrorResponse(error)
  }
}
