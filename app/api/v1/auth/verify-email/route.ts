import { NextRequest } from 'next/server'
import { getUsersCollection, getVerificationCodesCollection } from '@/lib/mongodb'
import { emailVerificationSchema } from '@/lib/models/user'
import { checkRateLimit, getClientIP, createErrorResponse, createSuccessResponse, AuthError, AuthErrorCodes, isExpired } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    // Rate limiting - 5 attempts per minute
    if (!checkRateLimit(`verify:${clientIP}`, 5, 60000)) {
      throw new AuthError('Too many verification attempts. Please try again later.', AuthErrorCodes.RATE_LIMIT_EXCEEDED, 429)
    }
    
    // Parse and validate request body
    const body = await request.json()
    const validationResult = emailVerificationSchema.safeParse(body)
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
      throw new AuthError(`Validation failed: ${errors}`, AuthErrorCodes.VALIDATION_ERROR)
    }
    
    const { email, code } = validationResult.data
    
    // Get database collections
    const usersCollection = await getUsersCollection()
    const verificationCollection = await getVerificationCodesCollection()
    
    // Find user
    const user = await usersCollection.findOne({ email: email.toLowerCase() })
    if (!user) {
      throw new AuthError('User not found', AuthErrorCodes.USER_NOT_FOUND, 404)
    }
    
    // Check if already verified
    if (user.isVerified) {
      return createSuccessResponse({
        message: 'Email already verified',
        alreadyVerified: true
      })
    }
    
    // Find verification code
    const verificationCode = await verificationCollection.findOne({
      email: email.toLowerCase(),
      type: 'email_verification',
      isUsed: false
    })
    
    if (!verificationCode) {
      throw new AuthError('No verification code found. Please request a new one.', AuthErrorCodes.INVALID_VERIFICATION_CODE)
    }
    
    // Check if expired
    if (isExpired(verificationCode.expiresAt)) {
      // Mark as used to prevent reuse
      await verificationCollection.updateOne(
        { _id: verificationCode._id },
        { $set: { isUsed: true } }
      )
      throw new AuthError('Verification code has expired. Please request a new one.', AuthErrorCodes.VERIFICATION_CODE_EXPIRED)
    }
    
    // Check attempts
    if (verificationCode.attempts >= verificationCode.maxAttempts) {
      await verificationCollection.updateOne(
        { _id: verificationCode._id },
        { $set: { isUsed: true } }
      )
      throw new AuthError('Too many verification attempts. Please request a new code.', AuthErrorCodes.TOO_MANY_ATTEMPTS)
    }
    
    // Verify code
    if (verificationCode.code !== code) {
      // Increment attempts
      await verificationCollection.updateOne(
        { _id: verificationCode._id },
        { $inc: { attempts: 1 } }
      )
      
      const remainingAttempts = verificationCode.maxAttempts - (verificationCode.attempts + 1)
      throw new AuthError(
        `Invalid verification code. ${remainingAttempts} attempts remaining.`,
        AuthErrorCodes.INVALID_VERIFICATION_CODE
      )
    }
    
    // Mark verification code as used
    await verificationCollection.updateOne(
      { _id: verificationCode._id },
      { $set: { isUsed: true } }
    )
    
    // Update user as verified
    await usersCollection.updateOne(
      { _id: user._id },
      { 
        $set: { 
          isVerified: true,
          updatedAt: new Date()
        } 
      }
    )
    
    // Clean up old verification codes for this email
    await verificationCollection.deleteMany({
      email: email.toLowerCase(),
      type: 'email_verification',
      _id: { $ne: verificationCode._id }
    })
    
    return createSuccessResponse({
      message: 'Email verified successfully',
      verified: true,
      nextStep: 'profile_setup'
    })
    
  } catch (error) {
    return createErrorResponse(error instanceof Error ? error : new Error('Unknown error'))
  }
}
