import { NextRequest } from 'next/server'
import { getUsersCollection, getVerificationCodesCollection, getSessionsCollection } from '@/lib/mongodb'
import { emailVerificationSchema, createSession } from '@/lib/models/user'
import { createJWT, checkRateLimit, getClientIP, getUserAgent, createErrorResponse, createSuccessResponse, AuthError, AuthErrorCodes, isExpired, sanitizeUser } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    const userAgent = getUserAgent(request)
    
    // Rate limiting - 5 attempts per minute
    if (!checkRateLimit(`verify-login:${clientIP}`, 5, 60000)) {
      throw new AuthError('Too many verification attempts. Please try again later.', AuthErrorCodes.RATE_LIMIT_EXCEEDED, 429)
    }
    
    // Parse and validate request body
    const body = await request.json()
    const validationResult = emailVerificationSchema.safeParse(body)
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
      throw new AuthError(`Validation failed: ${errors}`, AuthErrorCodes.VALIDATION_ERROR)
    }
    
    const { email, code } = validationResult.data
    
    // Get database collections
    const usersCollection = await getUsersCollection()
    const verificationCollection = await getVerificationCodesCollection()
    const sessionsCollection = await getSessionsCollection()
    
    // Find user
    const user = await usersCollection.findOne({ email: email.toLowerCase() })
    if (!user) {
      throw new AuthError('User not found', AuthErrorCodes.USER_NOT_FOUND, 404)
    }
    
    // Check if user is verified
    if (!user.isVerified) {
      throw new AuthError('Email must be verified before logging in', AuthErrorCodes.EMAIL_NOT_VERIFIED)
    }
    
    // Find verification code
    const verificationCode = await verificationCollection.findOne({
      email: email.toLowerCase(),
      type: 'login_verification',
      isUsed: false
    })
    
    if (!verificationCode) {
      throw new AuthError('No verification code found. Please request a new one.', AuthErrorCodes.INVALID_VERIFICATION_CODE)
    }
    
    // Check if expired
    if (isExpired(verificationCode.expiresAt)) {
      await verificationCollection.updateOne(
        { _id: verificationCode._id },
        { $set: { isUsed: true } }
      )
      throw new AuthError('Verification code has expired. Please request a new one.', AuthErrorCodes.VERIFICATION_CODE_EXPIRED)
    }
    
    // Check attempts
    if (verificationCode.attempts >= verificationCode.maxAttempts) {
      await verificationCollection.updateOne(
        { _id: verificationCode._id },
        { $set: { isUsed: true } }
      )
      throw new AuthError('Too many verification attempts. Please request a new code.', AuthErrorCodes.TOO_MANY_ATTEMPTS)
    }
    
    // Verify code
    if (verificationCode.code !== code) {
      await verificationCollection.updateOne(
        { _id: verificationCode._id },
        { $inc: { attempts: 1 } }
      )
      
      const remainingAttempts = verificationCode.maxAttempts - (verificationCode.attempts + 1)
      throw new AuthError(
        `Invalid verification code. ${remainingAttempts} attempts remaining.`,
        AuthErrorCodes.INVALID_VERIFICATION_CODE
      )
    }
    
    // Mark verification code as used
    await verificationCollection.updateOne(
      { _id: verificationCode._id },
      { $set: { isUsed: true } }
    )
    
    // Update user login metadata
    await usersCollection.updateOne(
      { _id: user._id },
      { 
        $set: { 
          'metadata.lastLoginAt': new Date(),
          'metadata.lastActiveAt': new Date(),
          updatedAt: new Date()
        },
        $inc: {
          'metadata.loginCount': 1
        }
      }
    )
    
    // Create session
    const sessionData = createSession(user._id, clientIP, userAgent)
    await sessionsCollection.insertOne(sessionData)
    
    // Create JWT token
    const token = await createJWT({
      userId: user._id.toString(),
      email: user.email,
      role: user.role
    })
    
    // Clean up old verification codes for this email
    await verificationCollection.deleteMany({
      email: email.toLowerCase(),
      type: 'login_verification',
      _id: { $ne: verificationCode._id }
    })
    
    // Determine redirect URL based on profile completion
    const redirectTo = user.isProfileComplete ? '/home' : '/auth/complete-profile'
    
    // Create response
    const response = createSuccessResponse({
      message: 'Login successful',
      user: sanitizeUser(user),
      profileComplete: user.isProfileComplete,
      redirectTo
    })
    
    // Set session cookie
    response.cookies.set('session', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 30 * 24 * 60 * 60, // 30 days
      path: '/'
    })
    
    return response
    
  } catch (error) {
    return createErrorResponse(error)
  }
}
