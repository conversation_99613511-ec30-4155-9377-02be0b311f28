import { NextRequest } from 'next/server'
import { ObjectId } from 'mongodb'
import { getUsersCollection, getVerificationCodesCollection } from '@/lib/mongodb'
import { signupSchema, createUser, createVerificationCode } from '@/lib/models/user'
import { hashPassword, checkRateLimit, getClientIP, getUserAgent, createErrorResponse, createSuccessResponse, AuthError, AuthErrorCodes } from '@/lib/auth'
import { sendVerificationEmail } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    const userAgent = getUserAgent(request)
    
    // Rate limiting
    if (!checkRateLimit(`register:${clientIP}`, 3, 300000)) { // 3 attempts per 5 minutes
      throw new AuthError('Too many registration attempts. Please try again later.', AuthErrorCodes.RATE_LIMIT_EXCEEDED, 429)
    }
    
    // Parse and validate request body
    const body = await request.json()
    const validationResult = signupSchema.safeParse(body)
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
      throw new AuthError(`Validation failed: ${errors}`, AuthErrorCodes.VALIDATION_ERROR)
    }
    
    const { firstName, lastName, username, email, password } = validationResult.data
    
    // Get database collections
    const usersCollection = await getUsersCollection()
    const verificationCollection = await getVerificationCodesCollection()
    
    // Check if user already exists
    const existingUser = await usersCollection.findOne({
      $or: [
        { email: email.toLowerCase() },
        { username: username.toLowerCase() }
      ]
    })
    
    if (existingUser) {
      if (existingUser.email === email.toLowerCase()) {
        throw new AuthError('An account with this email already exists', AuthErrorCodes.USER_ALREADY_EXISTS)
      } else {
        throw new AuthError('This username is already taken', AuthErrorCodes.USER_ALREADY_EXISTS)
      }
    }
    
    // Hash password
    const passwordHash = await hashPassword(password)
    
    // Create user document
    const userData = createUser({ firstName, lastName, username, email, password, confirmPassword: password })
    userData.passwordHash = passwordHash
    userData.metadata.registrationIp = clientIP
    
    // Insert user
    const result = await usersCollection.insertOne(userData)
    const userId = result.insertedId
    
    // Create verification code
    const verificationCodeData = createVerificationCode(email, 'email_verification', clientIP, userAgent)
    await verificationCollection.insertOne(verificationCodeData)
    
    // Send verification email
    const emailSent = await sendVerificationEmail(email, verificationCodeData.code, firstName)
    
    if (!emailSent) {
      console.error('Failed to send verification email for user:', userId)
      // Don't fail the registration, user can request resend
    }
    
    return createSuccessResponse({
      message: 'Registration successful. Please check your email for verification code.',
      userId: userId.toString(),
      email: email.toLowerCase(),
      emailSent
    }, 201)
    
  } catch (error) {
    return createErrorResponse(error)
  }
}

// Username availability check
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const username = searchParams.get('username')
    
    if (!username) {
      throw new AuthError('Username parameter is required', AuthErrorCodes.VALIDATION_ERROR)
    }
    
    // Validate username format
    const usernameRegex = /^[a-zA-Z0-9_-]+$/
    if (!usernameRegex.test(username) || username.length < 3 || username.length > 30) {
      return createSuccessResponse({ available: false, reason: 'Invalid username format' })
    }
    
    if (username.startsWith('_') || username.endsWith('_')) {
      return createSuccessResponse({ available: false, reason: 'Username cannot start or end with underscore' })
    }
    
    // Check database
    const usersCollection = await getUsersCollection()
    const existingUser = await usersCollection.findOne({ username: username.toLowerCase() })
    
    return createSuccessResponse({
      available: !existingUser,
      username: username.toLowerCase()
    })
    
  } catch (error) {
    return createErrorResponse(error)
  }
}
