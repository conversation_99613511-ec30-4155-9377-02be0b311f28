import { NextRequest } from 'next/server'
import { getUsersCollection, getVerificationCodesCollection } from '@/lib/mongodb'
import { signupSchema, createUser, createVerificationCode } from '@/lib/models/user'
import { hashPassword, checkRateLimit, getClientIP, getUserAgent, createErrorResponse, createSuccessResponse, AuthError, AuthErrorCodes } from '@/lib/auth'
import { sendVerificationEmail } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    const userAgent = getUserAgent(request)
    
    // Rate limiting
    if (!checkRateLimit(`register:${clientIP}`, 3, 300000)) { // 3 attempts per 5 minutes
      throw new AuthError('Too many registration attempts. Please try again later.', AuthErrorCodes.RATE_LIMIT_EXCEEDED, 429)
    }
    
    // Parse and validate request body
    const body = await request.json()
    const validationResult = signupSchema.safeParse(body)
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
      throw new AuthError(`Validation failed: ${errors}`, AuthErrorCodes.VALIDATION_ERROR)
    }
    
    const { firstName, username, email, password } = validationResult.data
    
    // Get database collections
    const usersCollection = await getUsersCollection()
    const verificationCollection = await getVerificationCodesCollection()
    
    // Check if user already exists
    const existingUser = await usersCollection.findOne({
      $or: [
        { email: email.toLowerCase() },
        { username: username.toLowerCase() }
      ]
    })
    
    if (existingUser) {
      if (existingUser.email === email.toLowerCase()) {
        throw new AuthError('An account with this email already exists', AuthErrorCodes.USER_ALREADY_EXISTS)
      } else {
        throw new AuthError('This username is already taken', AuthErrorCodes.USER_ALREADY_EXISTS)
      }
    }
    
    // Hash password
    const passwordHash = await hashPassword(password)
    
    // Create user document
    const userData = createUser({ ...validationResult.data, passwordHash })
    userData.metadata.registrationIp = clientIP
    
    // Insert user
    const result = await usersCollection.insertOne(userData)
    const userId = result.insertedId
    
    // Create verification code
    const verificationCodeData = createVerificationCode(email, 'email_verification', clientIP, userAgent)
    await verificationCollection.insertOne(verificationCodeData)
    
    // Send verification email
    const emailSent = await sendVerificationEmail(email, verificationCodeData.code, firstName)
    
    if (!emailSent) {
      console.error('Failed to send verification email for user:', userId)
      // Don't fail the registration, user can request resend
    }
    
    return createSuccessResponse({
      message: 'Registration successful. Please check your email for verification code.',
      userId: userId.toString(),
      email: email.toLowerCase(),
      emailSent
    }, 201)
    
  } catch (error) {
    return createErrorResponse(error instanceof Error ? error : new Error('Unknown error'))
  }
}


