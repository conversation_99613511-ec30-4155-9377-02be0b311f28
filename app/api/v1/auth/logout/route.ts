import { NextRequest } from 'next/server'
import { cookies } from 'next/headers'
import { getSessionsCollection } from '@/lib/mongodb'
import { verifyJWT, createErrorResponse, createSuccessResponse, AuthError, AuthErrorCodes } from '@/lib/auth'
import { ObjectId } from 'mongodb'

export async function POST(request: NextRequest) {
  try {
    // Get session token from cookies
    const cookieStore = await cookies()
    const sessionToken = cookieStore.get('session')?.value
    
    if (!sessionToken) {
      // Already logged out, return success
      const response = createSuccessResponse({
        message: 'Logged out successfully',
        loggedOut: true
      })
      
      // Clear session cookie
      response.cookies.set('session', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 0,
        path: '/'
      })
      
      return response
    }
    
    // Verify JWT token
    const tokenPayload = await verifyJWT(sessionToken)
    
    if (tokenPayload) {
      // Remove session from database
      const sessionsCollection = await getSessionsCollection()
      await sessionsCollection.deleteMany({
        userId: new ObjectId(tokenPayload.userId),
        sessionToken: sessionToken
      })
    }
    
    // Create response
    const response = createSuccessResponse({
      message: 'Logged out successfully',
      loggedOut: true
    })
    
    // Clear session cookie
    response.cookies.set('session', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/'
    })
    
    return response
    
  } catch (error) {
    // Even if there's an error, clear the cookie and return success
    const response = createSuccessResponse({
      message: 'Logged out successfully',
      loggedOut: true
    })
    
    response.cookies.set('session', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/'
    })
    
    return response
  }
}

// Logout from all devices
export async function DELETE(request: NextRequest) {
  try {
    // Get session token from cookies
    const cookieStore = await cookies()
    const sessionToken = cookieStore.get('session')?.value
    
    if (!sessionToken) {
      throw new AuthError('Authentication required', AuthErrorCodes.UNAUTHORIZED, 401)
    }
    
    // Verify JWT token
    const tokenPayload = await verifyJWT(sessionToken)
    if (!tokenPayload) {
      throw new AuthError('Invalid session', AuthErrorCodes.INVALID_SESSION, 401)
    }
    
    // Remove all sessions for this user
    const sessionsCollection = await getSessionsCollection()
    const deleteResult = await sessionsCollection.deleteMany({
      userId: new ObjectId(tokenPayload.userId)
    })
    
    // Create response
    const response = createSuccessResponse({
      message: 'Logged out from all devices successfully',
      loggedOut: true,
      sessionsRemoved: deleteResult.deletedCount
    })
    
    // Clear session cookie
    response.cookies.set('session', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/'
    })
    
    return response
    
  } catch (error) {
    return createErrorResponse(error)
  }
}
