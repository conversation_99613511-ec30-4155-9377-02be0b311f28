import { NextRequest } from 'next/server'
import { getUsersCollection } from '@/lib/mongodb'
import { createErrorResponse, createSuccessResponse, AuthError, AuthErrorCodes } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const username = searchParams.get('username')
    
    if (!username) {
      throw new AuthError('Username parameter is required', AuthErrorCodes.VALIDATION_ERROR)
    }
    
    // Validate username format
    const usernameRegex = /^[a-zA-Z0-9_-]+$/
    if (!usernameRegex.test(username) || username.length < 3 || username.length > 30) {
      return createSuccessResponse({ available: false, reason: 'Invalid username format' })
    }
    
    if (username.startsWith('_') || username.endsWith('_')) {
      return createSuccessResponse({ available: false, reason: 'Username cannot start or end with underscore' })
    }
    
    // Check database
    const usersCollection = await getUsersCollection()
    const existingUser = await usersCollection.findOne({ username: username.toLowerCase() })
    
    return createSuccessResponse({
      available: !existingUser,
      username: username.toLowerCase()
    })
    
  } catch (error) {
    return createErrorResponse(error instanceof Error ? error : new Error('Unknown error'))
  }
}
