import { NextRequest } from 'next/server'
import { cookies } from 'next/headers'
import { getUsersCollection } from '@/lib/mongodb'
import { profileSetupSchema } from '@/lib/models/user'
import { verifyJWT, createJWT, createErrorResponse, createSuccessResponse, AuthError, AuthErrorCodes, sanitizeUser } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    // Get session token from cookies
    const cookieStore = await cookies()
    const sessionToken = cookieStore.get('session')?.value
    
    if (!sessionToken) {
      throw new AuthError('Authentication required', AuthErrorCodes.UNAUTHORIZED, 401)
    }
    
    // Verify JWT token
    const tokenPayload = await verifyJWT(sessionToken)
    if (!tokenPayload) {
      throw new AuthError('Invalid session', AuthErrorCodes.INVALID_SESSION, 401)
    }
    
    // Parse and validate request body
    const body = await request.json()
    const validationResult = profileSetupSchema.safeParse(body)
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
      throw new AuthError(`Validation failed: ${errors}`, AuthErrorCodes.VALIDATION_ERROR)
    }
    
    const { githubUsername, discordUsername, description, position } = validationResult.data
    
    // Get user from database
    const usersCollection = await getUsersCollection()
    const user = await usersCollection.findOne({ email: tokenPayload.email })
    
    if (!user) {
      throw new AuthError('User not found', AuthErrorCodes.USER_NOT_FOUND, 404)
    }
    
    // Check if user is verified
    if (!user.isVerified) {
      throw new AuthError('Email must be verified before completing profile', AuthErrorCodes.EMAIL_NOT_VERIFIED)
    }
    
    // Check if profile is already complete
    if (user.isProfileComplete) {
      return createSuccessResponse({
        message: 'Profile already completed',
        user: sanitizeUser(user)
      })
    }
    
    // Check for duplicate GitHub username if provided
    if (githubUsername) {
      const existingGithubUser = await usersCollection.findOne({
        githubUsername: githubUsername.toLowerCase(),
        _id: { $ne: user._id }
      })
      
      if (existingGithubUser) {
        throw new AuthError('This GitHub username is already associated with another account', AuthErrorCodes.USER_ALREADY_EXISTS)
      }
    }
    
    // Check for duplicate Discord username if provided
    if (discordUsername) {
      const existingDiscordUser = await usersCollection.findOne({
        discordUsername: discordUsername.toLowerCase(),
        _id: { $ne: user._id }
      })
      
      if (existingDiscordUser) {
        throw new AuthError('This Discord username is already associated with another account', AuthErrorCodes.USER_ALREADY_EXISTS)
      }
    }
    
    // Update user profile
    const updateData: any = {
      bio: description.trim(),
      position,
      isProfileComplete: true,
      updatedAt: new Date()
    }
    
    if (githubUsername) {
      updateData.githubUsername = githubUsername.toLowerCase()
    }
    
    if (discordUsername) {
      updateData.discordUsername = discordUsername.toLowerCase()
    }
    
    const updateResult = await usersCollection.updateOne(
      { _id: user._id },
      { $set: updateData }
    )
    
    if (updateResult.matchedCount === 0) {
      throw new AuthError('Failed to update profile', AuthErrorCodes.INTERNAL_ERROR, 500)
    }
    
    // Get updated user
    const updatedUser = await usersCollection.findOne({ _id: user._id })
    if (!updatedUser) {
      throw new AuthError('Failed to retrieve updated user', AuthErrorCodes.INTERNAL_ERROR, 500)
    }
    
    // Create new JWT with updated user info
    const newToken = await createJWT({
      userId: updatedUser._id.toString(),
      email: updatedUser.email,
      role: updatedUser.role
    })
    
    // Set new session cookie
    const response = createSuccessResponse({
      message: 'Profile completed successfully',
      user: sanitizeUser(updatedUser),
      profileComplete: true,
      redirectTo: '/home'
    })
    
    response.cookies.set('session', newToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 30 * 24 * 60 * 60, // 30 days
      path: '/'
    })
    
    return response
    
  } catch (error) {
    return createErrorResponse(error)
  }
}
