import { NextRequest } from 'next/server'
import { getUsersCollection, getVerificationCodesCollection } from '@/lib/mongodb'
import { createVerificationCode } from '@/lib/models/user'
import { checkRateLimit, getClientIP, getUserAgent, createErrorResponse, createSuccessResponse, AuthError, AuthErrorCodes, getRateLimitInfo } from '@/lib/auth'
import { sendVerificationEmail, sendLoginVerificationEmail } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    const userAgent = getUserAgent(request)
    
    // Parse request body
    const body = await request.json()
    const { email, type = 'email_verification' } = body
    
    if (!email) {
      throw new AuthError('Email is required', AuthErrorCodes.VALIDATION_ERROR)
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email) || email.length > 255) {
      throw new AuthError('Invalid email format', AuthErrorCodes.VALIDATION_ERROR)
    }
    
    // Rate limiting - 3 codes per 5 minutes per email
    const rateLimitKey = `resend:${email.toLowerCase()}`
    if (!checkRateLimit(rateLimitKey, 3, 300000)) {
      const rateLimitInfo = getRateLimitInfo(rateLimitKey)
      const retryAfter = Math.ceil((rateLimitInfo.resetTime - Date.now()) / 1000)
      
      throw new AuthError(
        `Too many verification codes requested. Please try again in ${Math.ceil(retryAfter / 60)} minutes.`,
        AuthErrorCodes.RATE_LIMIT_EXCEEDED,
        429
      )
    }
    
    // Additional IP-based rate limiting
    if (!checkRateLimit(`resend-ip:${clientIP}`, 10, 3600000)) { // 10 codes per hour per IP
      throw new AuthError('Too many requests from this IP. Please try again later.', AuthErrorCodes.RATE_LIMIT_EXCEEDED, 429)
    }
    
    // Get database collections
    const usersCollection = await getUsersCollection()
    const verificationCollection = await getVerificationCodesCollection()
    
    // Find user
    const user = await usersCollection.findOne({ email: email.toLowerCase() })
    if (!user) {
      // Don't reveal if user exists or not for security
      return createSuccessResponse({
        message: 'If an account with this email exists, a verification code has been sent.',
        emailSent: true
      })
    }
    
    // Check verification type validity
    if (type === 'email_verification' && user.isVerified) {
      throw new AuthError('Email is already verified', AuthErrorCodes.VALIDATION_ERROR)
    }
    
    if (type === 'login_verification' && !user.isVerified) {
      throw new AuthError('Email must be verified before login verification', AuthErrorCodes.EMAIL_NOT_VERIFIED)
    }
    
    // Clean up old verification codes for this email and type
    await verificationCollection.deleteMany({
      email: email.toLowerCase(),
      type,
      $or: [
        { isUsed: true },
        { expiresAt: { $lt: new Date() } }
      ]
    })
    
    // Check for existing valid verification code
    const existingCode = await verificationCollection.findOne({
      email: email.toLowerCase(),
      type,
      isUsed: false,
      expiresAt: { $gt: new Date() }
    })
    
    if (existingCode) {
      // Check if it was created recently (within 60 seconds)
      const timeSinceCreation = Date.now() - existingCode.createdAt.getTime()
      if (timeSinceCreation < 60000) { // 60 seconds
        const retryAfter = Math.ceil((60000 - timeSinceCreation) / 1000)
        throw new AuthError(
          `Please wait ${retryAfter} seconds before requesting a new code.`,
          AuthErrorCodes.RATE_LIMIT_EXCEEDED,
          429
        )
      }
      
      // Mark old code as used
      await verificationCollection.updateOne(
        { _id: existingCode._id },
        { $set: { isUsed: true } }
      )
    }
    
    // Create new verification code
    const verificationCodeData = createVerificationCode(email.toLowerCase(), type, clientIP, userAgent)
    await verificationCollection.insertOne(verificationCodeData)
    
    // Send appropriate email
    let emailSent = false
    if (type === 'email_verification') {
      emailSent = await sendVerificationEmail(email, verificationCodeData.code, user.firstName)
    } else if (type === 'login_verification') {
      emailSent = await sendLoginVerificationEmail(email, verificationCodeData.code, user.firstName)
    }
    
    if (!emailSent) {
      console.error('Failed to send verification email:', { email, type, userId: user._id })
      throw new AuthError('Failed to send verification email. Please try again.', AuthErrorCodes.INTERNAL_ERROR, 500)
    }
    
    // Get rate limit info for response
    const rateLimitInfo = getRateLimitInfo(rateLimitKey)
    
    return createSuccessResponse({
      message: 'Verification code sent successfully',
      emailSent: true,
      expiresIn: 15 * 60, // 15 minutes in seconds
      remaining: rateLimitInfo.remaining,
      retryAfter: 60 // seconds until next code can be requested
    })
    
  } catch (error) {
    return createErrorResponse(error instanceof Error ? error : new Error('Unknown error'))
  }
}
