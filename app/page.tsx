import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import ModernProjectCard from "@/components/modern-project-card"
import { getFeaturedProjects } from "@/lib/data"
import { Search, Star, Code, Users, Sparkles, Zap, Rocket, Globe, Bot, Smartphone } from "lucide-react"

export default function HomePage() {
  const featuredProjects = getFeaturedProjects()

  return (
    <div className="min-h-screen bg-dark-950 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-72 h-72 bg-emerald-500/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute top-40 right-10 w-96 h-96 bg-violet-500/10 rounded-full blur-3xl animate-float-slow"></div>
        <div className="absolute bottom-20 left-1/3 w-80 h-80 bg-rose-500/10 rounded-full blur-3xl animate-float"></div>
      </div>

      {/* Hero Section */}
      <section className="relative py-32 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <div className="animate-slide-up">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-dark-800/50 border border-emerald-500/20 text-emerald-400 text-sm font-medium mb-8 backdrop-blur-sm">
              <Sparkles className="h-4 w-4 mr-2 animate-pulse" />
              Discover the Future of Open Source
            </div>

            <h1 className="text-5xl md:text-7xl font-bold text-white mb-8 leading-tight">
              Explore Amazing
              <span className="gradient-text block mt-2">Open Source Projects</span>
            </h1>

            <p className="text-xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
              Dive into a curated universe of innovative open-source projects from visionary developers worldwide. Find
              your next inspiration, contribute to the community, or showcase your own masterpiece.
            </p>
          </div>

          {/* Hero Search */}
          <div className="max-w-3xl mx-auto mb-12 animate-slide-up" style={{ animationDelay: "0.2s" }}>
            <form className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/20 to-violet-500/20 rounded-3xl blur-xl group-focus-within:blur-2xl transition-all duration-500"></div>
              <div className="relative flex items-center glass-effect rounded-3xl p-2">
                <div className="flex-1 relative">
                  <Input
                    type="text"
                    placeholder="Search for projects, developers, or technologies..."
                    className="w-full h-16 bg-transparent border-0 text-white placeholder-gray-400 text-lg pl-16 pr-8 focus:ring-0"
                  />
                  <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400 group-focus-within:text-emerald-400 transition-colors duration-300" />
                </div>
                <Button className="btn-primary h-12 px-8 ml-2">
                  <Zap className="h-5 w-5 mr-2" />
                  Explore
                </Button>
              </div>
            </form>
          </div>

          <div
            className="flex flex-col sm:flex-row gap-6 justify-center animate-slide-up"
            style={{ animationDelay: "0.4s" }}
          >
            <Link href="/browse">
              <Button className="btn-primary text-lg px-8 py-4">
                <Rocket className="h-5 w-5 mr-2" />
                Browse All Projects
              </Button>
            </Link>
            <Link href="/auth/signup">
              <Button className="btn-secondary text-lg px-8 py-4">
                <Users className="h-5 w-5 mr-2" />
                Join Community
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 stagger-animation">
            {[
              {
                icon: Code,
                count: "50+",
                label: "Open Source Projects",
                color: "text-emerald-400",
                bg: "bg-emerald-500/10",
              },
              {
                icon: Users,
                count: "25+",
                label: "Active Developers",
                color: "text-violet-400",
                bg: "bg-violet-500/10",
              },
              { icon: Star, count: "1000+", label: "GitHub Stars", color: "text-amber-400", bg: "bg-amber-500/10" },
            ].map((stat, index) => (
              <div key={index} className="text-center group">
                <div className="glass-card rounded-2xl p-8 hover-glow">
                  <div
                    className={`${stat.bg} rounded-2xl w-20 h-20 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <stat.icon className={`h-10 w-10 ${stat.color}`} />
                  </div>
                  <h3 className="text-4xl font-bold text-white mb-3">{stat.count}</h3>
                  <p className="text-gray-400 text-lg">{stat.label}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Projects */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-dark-800/50 border border-violet-500/20 text-violet-400 text-sm font-medium mb-6">
              <Star className="h-4 w-4 mr-2 animate-pulse" />
              Handpicked Excellence
            </div>
            <h2 className="text-4xl font-bold text-white mb-4">Featured Projects</h2>
            <p className="text-gray-400 text-lg max-w-2xl mx-auto">
              Discover our carefully curated selection of outstanding open-source projects that are pushing the
              boundaries of innovation.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {featuredProjects.map((project, index) => (
              <ModernProjectCard key={project.id} project={project} index={index} />
            ))}
          </div>

          <div className="text-center">
            <Link href="/browse">
              <Button className="btn-secondary text-lg px-8 py-4 group">
                View All Projects
                <Rocket className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Explore by Category</h2>
            <p className="text-gray-400 text-lg max-w-2xl mx-auto">
              Find exactly what you're looking for with our organized project categories.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 stagger-animation">
            {[
              {
                icon: Globe,
                title: "Websites",
                description: "Modern web applications and stunning sites",
                href: "/browse?tag=Website",
                gradient: "from-emerald-500 to-teal-500",
                count: "15+",
              },
              {
                icon: Bot,
                title: "Discord Bots",
                description: "Powerful bots for Discord communities",
                href: "/browse?tag=Discord Bot",
                gradient: "from-violet-500 to-purple-500",
                count: "8+",
              },
              {
                icon: Smartphone,
                title: "Applications",
                description: "Desktop and mobile applications",
                href: "/browse?tag=Apps",
                gradient: "from-rose-500 to-pink-500",
                count: "12+",
              },
            ].map((category, index) => (
              <Link key={index} href={category.href} className="group">
                <div className="glass-card rounded-2xl p-8 hover-glow relative overflow-hidden">
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${category.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}
                  ></div>

                  <div className="relative z-10">
                    <div
                      className={`bg-gradient-to-br ${category.gradient} rounded-2xl w-16 h-16 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}
                    >
                      <category.icon className="h-8 w-8 text-white" />
                    </div>

                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-2xl font-semibold text-white group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:bg-clip-text group-hover:from-white group-hover:to-gray-300 transition-all duration-300">
                        {category.title}
                      </h3>
                      <span className="text-sm text-gray-400 bg-dark-700 px-3 py-1 rounded-full">{category.count}</span>
                    </div>

                    <p className="text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors duration-300">
                      {category.description}
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
