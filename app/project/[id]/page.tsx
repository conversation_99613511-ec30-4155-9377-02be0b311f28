import { notFound } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { getProjectById } from "@/lib/data"
import { Calendar, User, Download, Github, ExternalLink, Clock, Sparkles } from "lucide-react"

interface ProjectPageProps {
  params: {
    id: string
  }
}

export default function ProjectPage({ params }: ProjectPageProps) {
  const project = getProjectById(params.id)

  if (!project) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-gray-950 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center text-sm text-gray-400 mb-4">
            <Link href="/" className="hover:text-white transition-colors">
              Home
            </Link>
            <span className="mx-2">/</span>
            <Link href="/browse" className="hover:text-white transition-colors">
              Browse
            </Link>
            <span className="mx-2">/</span>
            <span className="text-white">{project.title}</span>
          </div>

          <h1 className="text-4xl font-bold text-white mb-4">{project.title}</h1>

          <div className="flex flex-wrap items-center gap-4 text-gray-400 mb-6">
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4" />
              <span>by</span>
              <Link
                href={`/developer/${project.developer.id}`}
                className="text-blue-400 hover:text-blue-300 transition-colors"
              >
                {project.developer.name}
              </Link>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span>Created {new Date(project.createdDate).toLocaleDateString()}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span>Updated {new Date(project.lastUpdated).toLocaleDateString()}</span>
            </div>
          </div>

          <div className="flex flex-wrap gap-2 mb-6">
            {project.tags.map((tag) => (
              <Link key={tag} href={`/browse?tag=${encodeURIComponent(tag)}`}>
                <Badge
                  variant="secondary"
                  className="bg-gray-700 text-gray-300 hover:bg-gray-600 cursor-pointer transition-colors"
                >
                  {tag}
                </Badge>
              </Link>
            ))}
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <Link href={project.downloadUrl} className="flex-1">
              <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                <Download className="h-4 w-4 mr-2" />
                Download / View Developer
              </Button>
            </Link>
            <a href={project.githubUrl} target="_blank" rel="noopener noreferrer" className="flex-1">
              <Button
                variant="outline"
                className="w-full border-gray-600 text-gray-300 hover:bg-gray-800 bg-transparent"
              >
                <Github className="h-4 w-4 mr-2" />
                View GitHub Repo
              </Button>
            </a>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Description */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">About This Project</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 leading-relaxed">{project.detailedDescription}</p>
              </CardContent>
            </Card>

            {/* What's New */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Sparkles className="h-5 w-5 mr-2 text-yellow-400" />
                  {"What's New"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 leading-relaxed">{project.whatsNew}</p>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Developer Info */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Developer</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-3 mb-4">
                  <Image
                    src={project.developer.avatar || "/placeholder.svg"}
                    alt={project.developer.name}
                    width={48}
                    height={48}
                    className="rounded-full"
                  />
                  <div>
                    <Link
                      href={`/developer/${project.developer.id}`}
                      className="text-white font-semibold hover:text-blue-400 transition-colors"
                    >
                      {project.developer.name}
                    </Link>
                    <p className="text-gray-400 text-sm">{project.developer.email}</p>
                  </div>
                </div>
                <p className="text-gray-300 text-sm mb-4 line-clamp-3">{project.developer.bio}</p>
                <div className="flex space-x-2">
                  <Link href={`/developer/${project.developer.id}`} className="flex-1">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full border-gray-600 text-gray-300 hover:bg-gray-700 bg-transparent"
                    >
                      View Profile
                    </Button>
                  </Link>
                  <a
                    href={project.developer.githubProfile}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1"
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full border-gray-600 text-gray-300 hover:bg-gray-700 bg-transparent"
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      GitHub
                    </Button>
                  </a>
                </div>
              </CardContent>
            </Card>

            {/* Project Stats */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Project Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-gray-400 text-sm">Created</p>
                  <p className="text-white">
                    {new Date(project.createdDate).toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </p>
                </div>
                <div>
                  <p className="text-gray-400 text-sm">Last Updated</p>
                  <p className="text-white">
                    {new Date(project.lastUpdated).toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </p>
                </div>
                <div>
                  <p className="text-gray-400 text-sm">Category</p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {project.tags.slice(0, 2).map((tag) => (
                      <Badge key={tag} variant="secondary" className="bg-gray-700 text-gray-300 text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
