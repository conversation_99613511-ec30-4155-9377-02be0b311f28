import bcrypt from 'bcryptjs'
import { SignJWT, jwtVerify } from 'jose'
import { ObjectId } from 'mongodb'
import { User, UserSession } from './models/user'

const JWT_SECRET = new TextEncoder().encode(process.env.NEXTAUTH_SECRET || 'fallback-secret-key')
const SALT_ROUNDS = 12

// Password utilities
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, SALT_ROUNDS)
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

// JWT utilities
export async function createJWT(payload: { userId: string; email: string; role: string }): Promise<string> {
  return new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('30d')
    .sign(JWT_SECRET)
}

export async function verifyJWT(token: string): Promise<{ userId: string; email: string; role: string } | null> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET)
    return payload as { userId: string; email: string; role: string }
  } catch (error) {
    console.error('JWT verification failed:', error)
    return null
  }
}

// Rate limiting utilities
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

export function checkRateLimit(identifier: string, maxAttempts: number = 5, windowMs: number = 60000): boolean {
  const now = Date.now()
  const record = rateLimitMap.get(identifier)
  
  if (!record || now > record.resetTime) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (record.count >= maxAttempts) {
    return false
  }
  
  record.count++
  return true
}

export function getRateLimitInfo(identifier: string): { remaining: number; resetTime: number } {
  const record = rateLimitMap.get(identifier)
  const now = Date.now()
  
  if (!record || now > record.resetTime) {
    return { remaining: 5, resetTime: now + 60000 }
  }
  
  return {
    remaining: Math.max(0, 5 - record.count),
    resetTime: record.resetTime
  }
}

// Session utilities
export function generateSessionToken(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < 64; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// Request utilities
export function getClientIP(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const remoteAddr = request.headers.get('remote-addr')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  return realIP || remoteAddr || 'unknown'
}

export function getUserAgent(request: Request): string {
  return request.headers.get('user-agent') || 'unknown'
}

// Validation utilities
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email) && email.length <= 255
}

export function isValidUsername(username: string): boolean {
  const usernameRegex = /^[a-zA-Z0-9_-]+$/
  return usernameRegex.test(username) && 
         username.length >= 3 && 
         username.length <= 30 &&
         !username.startsWith('_') && 
         !username.endsWith('_')
}

export function isValidPassword(password: string): boolean {
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
  return passwordRegex.test(password) && password.length >= 8 && password.length <= 128
}

// Security utilities
export function sanitizeInput(input: string): string {
  return input.trim().replace(/[<>]/g, '')
}

export function isValidVerificationCode(code: string): boolean {
  return /^\d{6}$/.test(code)
}

// Error handling
export class AuthError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 400
  ) {
    super(message)
    this.name = 'AuthError'
  }
}

export const AuthErrorCodes = {
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
  EMAIL_NOT_VERIFIED: 'EMAIL_NOT_VERIFIED',
  INVALID_VERIFICATION_CODE: 'INVALID_VERIFICATION_CODE',
  VERIFICATION_CODE_EXPIRED: 'VERIFICATION_CODE_EXPIRED',
  TOO_MANY_ATTEMPTS: 'TOO_MANY_ATTEMPTS',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  INVALID_SESSION: 'INVALID_SESSION',
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INTERNAL_ERROR: 'INTERNAL_ERROR'
} as const

// Response utilities
export function createErrorResponse(error: AuthError | Error, statusCode?: number) {
  if (error instanceof AuthError) {
    return Response.json(
      { 
        success: false, 
        error: error.message, 
        code: error.code 
      },
      { status: error.statusCode }
    )
  }
  
  console.error('Unexpected error:', error)
  return Response.json(
    { 
      success: false, 
      error: 'Internal server error', 
      code: AuthErrorCodes.INTERNAL_ERROR 
    },
    { status: statusCode || 500 }
  )
}

export function createSuccessResponse(data: any, statusCode: number = 200) {
  return Response.json(
    { 
      success: true, 
      data 
    },
    { status: statusCode }
  )
}

// User utilities
export function sanitizeUser(user: User): Omit<User, 'passwordHash'> {
  const { passwordHash, ...sanitizedUser } = user
  return sanitizedUser
}

export function isUserProfileComplete(user: User): boolean {
  return user.isVerified && 
         user.isProfileComplete && 
         !!user.bio && 
         !!user.position
}

// Time utilities
export function isExpired(date: Date): boolean {
  return new Date() > date
}

export function getTimeUntilExpiry(date: Date): number {
  return Math.max(0, date.getTime() - Date.now())
}
