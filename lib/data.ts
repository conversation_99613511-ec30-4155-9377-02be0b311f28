export interface Project {
  id: string
  title: string
  description: string
  detailedDescription: string
  developer: <PERSON>eloper
  createdDate: string
  lastUpdated: string
  whatsNew: string
  tags: string[]
  githubUrl: string
  downloadUrl: string
  featured: boolean
}

export interface Developer {
  id: string
  name: string
  email: string
  bio: string
  avatar: string
  githubProfile: string
  joinDate: string
}

export const developers: Developer[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    bio: "Full-stack developer passionate about open source and modern web technologies. Specializes in React, Node.js, and cloud architecture.",
    avatar: "/placeholder.svg?height=100&width=100",
    githubProfile: "https://github.com/alexchen",
    joinDate: "2022-01-15",
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    bio: "Frontend developer and UI/UX enthusiast. Loves creating beautiful and accessible user interfaces with React and TypeScript.",
    avatar: "/placeholder.svg?height=100&width=100",
    githubProfile: "https://github.com/sarah<PERSON><PERSON><PERSON>",
    joinDate: "2021-08-20",
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    bio: "DevOps engineer and automation specialist. Focuses on CI/CD, containerization, and infrastructure as code.",
    avatar: "/placeholder.svg?height=100&width=100",
    githubProfile: "https://github.com/mikerodriguez",
    joinDate: "2023-03-10",
  },
]

export const projects: Project[] = [
  {
    id: "1",
    title: "TaskFlow Pro",
    description: "A modern task management application with real-time collaboration features.",
    detailedDescription:
      "TaskFlow Pro is a comprehensive task management solution built with React and Node.js. It features real-time collaboration, drag-and-drop task organization, team workspaces, and advanced reporting capabilities. The application uses WebSocket connections for instant updates and includes a robust notification system.",
    developer: developers[0],
    createdDate: "2023-06-15",
    lastUpdated: "2024-01-10",
    whatsNew:
      "Added dark mode support, improved performance by 40%, and introduced new collaboration features including real-time cursors and live editing.",
    tags: ["Website", "React", "Node.js", "Collaboration"],
    githubUrl: "https://github.com/alexchen/taskflow-pro",
    downloadUrl: "/developer/1",
    featured: true,
  },
  {
    id: "2",
    title: "Discord Music Bot",
    description: "Feature-rich Discord bot for playing music with queue management and playlist support.",
    detailedDescription:
      "A powerful Discord music bot built with Discord.js that supports multiple music sources including YouTube, Spotify, and SoundCloud. Features include queue management, playlist creation, volume control, and advanced audio filters. The bot supports slash commands and has a web dashboard for server management.",
    developer: developers[1],
    createdDate: "2023-04-20",
    lastUpdated: "2024-01-05",
    whatsNew:
      "Added Spotify integration, implemented audio filters (bass boost, nightcore), and improved queue management with shuffle and repeat modes.",
    tags: ["Discord Bot", "JavaScript", "Music", "Entertainment"],
    githubUrl: "https://github.com/sarahjohnson/discord-music-bot",
    downloadUrl: "/developer/2",
    featured: true,
  },
  {
    id: "3",
    title: "DevTools Suite",
    description: "Collection of essential development tools including code formatters and validators.",
    detailedDescription:
      "DevTools Suite is a comprehensive collection of development utilities including JSON formatter, regex tester, base64 encoder/decoder, color palette generator, and API testing tools. Built as a Progressive Web App with offline support and a clean, intuitive interface.",
    developer: developers[2],
    createdDate: "2023-08-10",
    lastUpdated: "2023-12-20",
    whatsNew:
      "Added new tools: SQL formatter, markdown preview, and JWT decoder. Improved offline functionality and added export options for all tools.",
    tags: ["Apps", "Tools", "PWA", "Utilities"],
    githubUrl: "https://github.com/mikerodriguez/devtools-suite",
    downloadUrl: "/developer/3",
    featured: false,
  },
  {
    id: "4",
    title: "EcoTracker",
    description: "Environmental impact tracking application for individuals and organizations.",
    detailedDescription:
      "EcoTracker helps users monitor and reduce their environmental footprint through comprehensive tracking of carbon emissions, energy usage, and waste production. Features include personalized recommendations, progress visualization, and community challenges to promote sustainable living.",
    developer: developers[0],
    createdDate: "2023-09-05",
    lastUpdated: "2024-01-08",
    whatsNew:
      "Introduced community features, added support for renewable energy tracking, and implemented AI-powered sustainability recommendations.",
    tags: ["Website", "Environment", "React", "Sustainability"],
    githubUrl: "https://github.com/alexchen/ecotracker",
    downloadUrl: "/developer/1",
    featured: true,
  },
  {
    id: "5",
    title: "Code Review Assistant",
    description: "AI-powered tool to help developers improve code quality and catch potential issues.",
    detailedDescription:
      "An intelligent code review assistant that analyzes pull requests and provides suggestions for improvements. Uses machine learning to identify potential bugs, security vulnerabilities, and code style issues. Integrates with popular version control systems and supports multiple programming languages.",
    developer: developers[1],
    createdDate: "2023-07-12",
    lastUpdated: "2023-12-28",
    whatsNew:
      "Enhanced AI model with better accuracy, added support for Python and Go, and introduced team-specific coding standards configuration.",
    tags: ["Apps", "AI", "Code Quality", "Development"],
    githubUrl: "https://github.com/sarahjohnson/code-review-assistant",
    downloadUrl: "/developer/2",
    featured: false,
  },
  {
    id: "6",
    title: "Fitness Tracker Bot",
    description: "Discord bot for tracking workouts and fitness goals with your server community.",
    detailedDescription:
      "A comprehensive fitness tracking Discord bot that allows users to log workouts, set fitness goals, and compete with friends. Features include exercise databases, progress tracking, leaderboards, and motivational reminders. Perfect for fitness communities and accountability groups.",
    developer: developers[2],
    createdDate: "2023-05-30",
    lastUpdated: "2024-01-03",
    whatsNew:
      "Added nutrition tracking, implemented workout challenges, and created detailed progress analytics with charts and graphs.",
    tags: ["Discord Bot", "Fitness", "Health", "Community"],
    githubUrl: "https://github.com/mikerodriguez/fitness-tracker-bot",
    downloadUrl: "/developer/3",
    featured: true,
  },
]

export function getProjectById(id: string): Project | undefined {
  return projects.find((project) => project.id === id)
}

export function getDeveloperById(id: string): Developer | undefined {
  return developers.find((developer) => developer.id === id)
}

export function getProjectsByDeveloper(developerId: string): Project[] {
  return projects.filter((project) => project.developer.id === developerId)
}

export function getFeaturedProjects(): Project[] {
  return projects.filter((project) => project.featured)
}

export function searchProjects(query: string): Project[] {
  const lowercaseQuery = query.toLowerCase()
  return projects.filter(
    (project) =>
      project.title.toLowerCase().includes(lowercaseQuery) ||
      project.description.toLowerCase().includes(lowercaseQuery) ||
      project.tags.some((tag) => tag.toLowerCase().includes(lowercaseQuery)) ||
      project.developer.name.toLowerCase().includes(lowercaseQuery),
  )
}

export function getProjectsByTag(tag: string): Project[] {
  return projects.filter((project) => project.tags.some((projectTag) => projectTag.toLowerCase() === tag.toLowerCase()))
}
