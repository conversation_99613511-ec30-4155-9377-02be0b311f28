import nodemailer from 'nodemailer'

// Email configuration
const emailConfig = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
}

// Create transporter
let transporter: nodemailer.Transporter | null = null

function getTransporter() {
  if (!transporter) {
    if (!process.env.SMTP_USER || !process.env.SMTP_PASS) {
      throw new Error('SMTP credentials not configured')
    }
    transporter = nodemailer.createTransporter(emailConfig)
  }
  return transporter
}

// Email templates
export const emailTemplates = {
  verification: (code: string, firstName: string) => ({
    subject: 'Verify Your AveExplore Account',
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Verify Your Account</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #0a0a0a; color: #ffffff; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; padding: 40px 0; }
            .logo { font-size: 32px; font-weight: bold; color: #10b981; margin-bottom: 10px; }
            .content { background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(139, 92, 246, 0.1)); border-radius: 16px; padding: 40px; margin: 20px 0; border: 1px solid rgba(255, 255, 255, 0.1); }
            .code-container { background: rgba(16, 185, 129, 0.2); border-radius: 12px; padding: 30px; text-align: center; margin: 30px 0; border: 1px solid rgba(16, 185, 129, 0.3); }
            .code { font-size: 36px; font-weight: bold; letter-spacing: 8px; color: #10b981; font-family: 'Courier New', monospace; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
            .button { display: inline-block; background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: 500; margin: 20px 0; }
            h1 { color: #ffffff; margin-bottom: 20px; }
            p { line-height: 1.6; color: #e5e5e5; margin-bottom: 16px; }
            .warning { background: rgba(239, 68, 68, 0.1); border: 1px solid rgba(239, 68, 68, 0.3); border-radius: 8px; padding: 16px; margin: 20px 0; color: #fca5a5; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">AveExplore</div>
              <p>Discover Amazing Open Source Projects</p>
            </div>
            
            <div class="content">
              <h1>Welcome to AveExplore, ${firstName}!</h1>
              <p>Thank you for joining our community of open source developers. To complete your registration, please verify your email address using the code below:</p>
              
              <div class="code-container">
                <div class="code">${code}</div>
                <p style="margin-top: 15px; color: #10b981; font-weight: 500;">Enter this code in the verification form</p>
              </div>
              
              <div class="warning">
                <strong>⚠️ Security Notice:</strong> This code will expire in 15 minutes. If you didn't request this verification, please ignore this email.
              </div>
              
              <p>Once verified, you'll be able to:</p>
              <ul style="color: #e5e5e5; line-height: 1.8;">
                <li>Submit your own open source projects</li>
                <li>Discover amazing projects from other developers</li>
                <li>Connect with the developer community</li>
                <li>Showcase your work to the world</li>
              </ul>
            </div>
            
            <div class="footer">
              <p>This email was sent to verify your AveExplore account. If you didn't sign up, you can safely ignore this email.</p>
              <p>&copy; 2024 AveExplore. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
      Welcome to AveExplore, ${firstName}!
      
      Your verification code is: ${code}
      
      This code will expire in 15 minutes.
      
      If you didn't request this verification, please ignore this email.
      
      © 2024 AveExplore. All rights reserved.
    `
  }),

  loginVerification: (code: string, firstName: string) => ({
    subject: 'AveExplore Login Verification',
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Login Verification</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #0a0a0a; color: #ffffff; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; padding: 40px 0; }
            .logo { font-size: 32px; font-weight: bold; color: #10b981; margin-bottom: 10px; }
            .content { background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(139, 92, 246, 0.1)); border-radius: 16px; padding: 40px; margin: 20px 0; border: 1px solid rgba(255, 255, 255, 0.1); }
            .code-container { background: rgba(16, 185, 129, 0.2); border-radius: 12px; padding: 30px; text-align: center; margin: 30px 0; border: 1px solid rgba(16, 185, 129, 0.3); }
            .code { font-size: 36px; font-weight: bold; letter-spacing: 8px; color: #10b981; font-family: 'Courier New', monospace; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
            h1 { color: #ffffff; margin-bottom: 20px; }
            p { line-height: 1.6; color: #e5e5e5; margin-bottom: 16px; }
            .warning { background: rgba(239, 68, 68, 0.1); border: 1px solid rgba(239, 68, 68, 0.3); border-radius: 8px; padding: 16px; margin: 20px 0; color: #fca5a5; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">AveExplore</div>
              <p>Discover Amazing Open Source Projects</p>
            </div>
            
            <div class="content">
              <h1>Login Verification Required</h1>
              <p>Hello ${firstName}, we detected a login attempt to your AveExplore account. Please use the verification code below to complete your login:</p>
              
              <div class="code-container">
                <div class="code">${code}</div>
                <p style="margin-top: 15px; color: #10b981; font-weight: 500;">Enter this code to complete your login</p>
              </div>
              
              <div class="warning">
                <strong>⚠️ Security Notice:</strong> This code will expire in 15 minutes. If you didn't attempt to log in, please secure your account immediately.
              </div>
            </div>
            
            <div class="footer">
              <p>This email was sent to verify your login attempt. If you didn't try to log in, please contact support.</p>
              <p>&copy; 2024 AveExplore. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
      Login Verification Required
      
      Hello ${firstName}, your login verification code is: ${code}
      
      This code will expire in 15 minutes.
      
      If you didn't attempt to log in, please secure your account immediately.
      
      © 2024 AveExplore. All rights reserved.
    `
  })
}

// Email sending functions
export async function sendVerificationEmail(email: string, code: string, firstName: string): Promise<boolean> {
  try {
    const transporter = getTransporter()
    const template = emailTemplates.verification(code, firstName)
    
    await transporter.sendMail({
      from: `"AveExplore" <${process.env.SMTP_USER}>`,
      to: email,
      subject: template.subject,
      html: template.html,
      text: template.text,
    })
    
    return true
  } catch (error) {
    console.error('Failed to send verification email:', error)
    return false
  }
}

export async function sendLoginVerificationEmail(email: string, code: string, firstName: string): Promise<boolean> {
  try {
    const transporter = getTransporter()
    const template = emailTemplates.loginVerification(code, firstName)
    
    await transporter.sendMail({
      from: `"AveExplore" <${process.env.SMTP_USER}>`,
      to: email,
      subject: template.subject,
      html: template.html,
      text: template.text,
    })
    
    return true
  } catch (error) {
    console.error('Failed to send login verification email:', error)
    return false
  }
}

// Email validation
export function isValidEmailFormat(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email) && email.length <= 255
}

// Test email connection
export async function testEmailConnection(): Promise<boolean> {
  try {
    const transporter = getTransporter()
    await transporter.verify()
    return true
  } catch (error) {
    console.error('Email connection test failed:', error)
    return false
  }
}
