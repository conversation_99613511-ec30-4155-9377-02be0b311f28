import { MongoClient, Db, Collection } from 'mongodb'

if (!process.env.MONGODB_URI) {
  throw new Error('Invalid/Missing environment variable: "MONGODB_URI"')
}

if (!process.env.MONGODB_DB_NAME) {
  throw new Error('Invalid/Missing environment variable: "MONGODB_DB_NAME"')
}

const uri = process.env.MONGODB_URI
const dbName = process.env.MONGODB_DB_NAME
const options = {
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  bufferMaxEntries: 0,
  bufferCommands: false,
}

let client: MongoClient
let clientPromise: Promise<MongoClient>

if (process.env.NODE_ENV === 'development') {
  // In development mode, use a global variable so that the value
  // is preserved across module reloads caused by HMR (Hot Module Replacement).
  let globalWithMongo = global as typeof globalThis & {
    _mongoClientPromise?: Promise<MongoClient>
  }

  if (!globalWithMongo._mongoClientPromise) {
    client = new MongoClient(uri, options)
    globalWithMongo._mongoClientPromise = client.connect()
  }
  clientPromise = globalWithMongo._mongoClientPromise
} else {
  // In production mode, it's best to not use a global variable.
  client = new MongoClient(uri, options)
  clientPromise = client.connect()
}

// Database connection helper
export async function connectToDatabase(): Promise<{ client: MongoClient; db: Db }> {
  try {
    const client = await clientPromise
    const db = client.db(dbName)
    return { client, db }
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error)
    throw new Error('Database connection failed')
  }
}

// Collection helpers
export async function getUsersCollection(): Promise<Collection> {
  const { db } = await connectToDatabase()
  return db.collection('users')
}

export async function getVerificationCodesCollection(): Promise<Collection> {
  const { db } = await connectToDatabase()
  return db.collection('verification_codes')
}

export async function getSessionsCollection(): Promise<Collection> {
  const { db } = await connectToDatabase()
  return db.collection('sessions')
}

export async function getProjectsCollection(): Promise<Collection> {
  const { db } = await connectToDatabase()
  return db.collection('projects')
}

export async function getTagsCollection(): Promise<Collection> {
  const { db } = await connectToDatabase()
  return db.collection('tags')
}

// Database initialization with indexes
export async function initializeDatabase() {
  try {
    const { db } = await connectToDatabase()
    
    // Users collection indexes
    const usersCollection = db.collection('users')
    await usersCollection.createIndex({ email: 1 }, { unique: true })
    await usersCollection.createIndex({ username: 1 }, { unique: true })
    await usersCollection.createIndex({ createdAt: -1 })
    await usersCollection.createIndex({ isVerified: 1 })
    
    // Verification codes collection indexes
    const verificationCollection = db.collection('verification_codes')
    await verificationCollection.createIndex({ email: 1 })
    await verificationCollection.createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 })
    await verificationCollection.createIndex({ attempts: 1 })
    
    // Sessions collection indexes
    const sessionsCollection = db.collection('sessions')
    await sessionsCollection.createIndex({ sessionToken: 1 }, { unique: true })
    await sessionsCollection.createIndex({ userId: 1 })
    await sessionsCollection.createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 })
    
    // Projects collection indexes
    const projectsCollection = db.collection('projects')
    await projectsCollection.createIndex({ userId: 1 })
    await projectsCollection.createIndex({ status: 1, featured: -1, createdAt: -1 })
    await projectsCollection.createIndex({ tags: 1 })
    await projectsCollection.createIndex({ 'stats.views': -1 })
    await projectsCollection.createIndex({
      title: 'text',
      description: 'text',
      'metadata.searchKeywords': 'text'
    })
    
    // Tags collection indexes
    const tagsCollection = db.collection('tags')
    await tagsCollection.createIndex({ name: 1 }, { unique: true })
    await tagsCollection.createIndex({ slug: 1 }, { unique: true })
    await tagsCollection.createIndex({ usageCount: -1 })
    
    console.log('Database indexes initialized successfully')
  } catch (error) {
    console.error('Failed to initialize database indexes:', error)
    throw error
  }
}

// Health check
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    const { db } = await connectToDatabase()
    await db.admin().ping()
    return true
  } catch (error) {
    console.error('Database health check failed:', error)
    return false
  }
}

export default clientPromise
