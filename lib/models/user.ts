import { ObjectId } from 'mongodb'
import { z } from 'zod'

// User role enum
export const UserRole = {
  USER: 'user',
  ADMIN: 'admin'
} as const

export type UserRoleType = typeof UserRole[keyof typeof UserRole]

// Position/role options
export const UserPosition = {
  PROGRAMMER: 'Programmer',
  NODEJS_DEV: 'Node.js Developer',
  WEBSITE_DEV: 'Website Developer',
  DISCORD_BOT_DEV: 'Discord Bot Developer',
  APP_DEV: 'App Developer',
  FULLSTACK_DEV: 'Full Stack Developer',
  FRONTEND_DEV: 'Frontend Developer',
  BACKEND_DEV: 'Backend Developer',
  MOBILE_DEV: 'Mobile Developer',
  GAME_DEV: 'Game Developer',
  DEVOPS: 'DevOps Engineer',
  DATA_SCIENTIST: 'Data Scientist',
  UI_UX_DESIGNER: 'UI/UX Designer',
  OTHER: 'Other'
} as const

export type UserPositionType = typeof UserPosition[keyof typeof UserPosition]

// User interface
export interface User {
  _id?: ObjectId
  email: string
  username: string
  firstName: string
  lastName: string
  fullName: string
  passwordHash: string
  bio?: string
  avatarUrl?: string
  githubUsername?: string
  discordUsername?: string
  position?: UserPositionType
  role: UserRoleType
  isVerified: boolean
  isProfileComplete: boolean
  preferences: {
    emailNotifications: boolean
    profileVisibility: 'public' | 'private'
  }
  metadata: {
    lastLoginAt?: Date
    loginCount: number
    registrationIp?: string
    lastActiveAt?: Date
  }
  createdAt: Date
  updatedAt: Date
}

// Verification code interface
export interface VerificationCode {
  _id?: ObjectId
  email: string
  code: string
  type: 'email_verification' | 'login_verification' | 'password_reset'
  attempts: number
  maxAttempts: number
  expiresAt: Date
  createdAt: Date
  isUsed: boolean
  ipAddress?: string
  userAgent?: string
}

// Session interface
export interface UserSession {
  _id?: ObjectId
  userId: ObjectId
  sessionToken: string
  expiresAt: Date
  userAgent?: string
  ipAddress?: string
  createdAt: Date
}

// Validation schemas
export const signupSchema = z.object({
  firstName: z.string()
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .regex(/^[a-zA-Z\s]+$/, 'First name can only contain letters and spaces'),
  
  lastName: z.string()
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .regex(/^[a-zA-Z\s]+$/, 'Last name can only contain letters and spaces'),
  
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(30, 'Username must be less than 30 characters')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens')
    .refine(val => !val.startsWith('_') && !val.endsWith('_'), 'Username cannot start or end with underscore'),
  
  email: z.string()
    .email('Please enter a valid email address')
    .max(255, 'Email must be less than 255 characters'),
  
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must be less than 128 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  
  confirmPassword: z.string()
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

export const signinSchema = z.object({
  emailOrUsername: z.string()
    .min(1, 'Email or username is required')
    .max(255, 'Input must be less than 255 characters'),
  
  password: z.string()
    .min(1, 'Password is required')
})

export const emailVerificationSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  code: z.string()
    .length(6, 'Verification code must be 6 digits')
    .regex(/^\d{6}$/, 'Verification code must contain only numbers')
})

export const profileSetupSchema = z.object({
  githubUsername: z.string()
    .max(39, 'GitHub username must be less than 39 characters')
    .regex(/^[a-zA-Z0-9]([a-zA-Z0-9-])*[a-zA-Z0-9]$|^[a-zA-Z0-9]$/, 'Invalid GitHub username format')
    .optional()
    .or(z.literal('')),
  
  discordUsername: z.string()
    .max(32, 'Discord username must be less than 32 characters')
    .regex(/^[a-zA-Z0-9._]+$/, 'Discord username can only contain letters, numbers, dots, and underscores')
    .optional()
    .or(z.literal('')),
  
  description: z.string()
    .min(10, 'Description must be at least 10 characters')
    .max(500, 'Description must be less than 500 characters'),
  
  position: z.enum(Object.values(UserPosition) as [UserPositionType, ...UserPositionType[]])
})

export type SignupFormData = z.infer<typeof signupSchema>
export type SigninFormData = z.infer<typeof signinSchema>
export type EmailVerificationData = z.infer<typeof emailVerificationSchema>
export type ProfileSetupData = z.infer<typeof profileSetupSchema>

// Helper functions
export function createUser(data: SignupFormData & { passwordHash: string }): Omit<User, '_id'> {
  const now = new Date()
  
  return {
    email: data.email.toLowerCase(),
    username: data.username.toLowerCase(),
    firstName: data.firstName.trim(),
    lastName: data.lastName.trim(),
    fullName: `${data.firstName.trim()} ${data.lastName.trim()}`,
    passwordHash: data.passwordHash,
    role: UserRole.USER,
    isVerified: false,
    isProfileComplete: false,
    preferences: {
      emailNotifications: true,
      profileVisibility: 'public'
    },
    metadata: {
      loginCount: 0,
      registrationIp: undefined,
      lastActiveAt: now
    },
    createdAt: now,
    updatedAt: now
  }
}

export function createVerificationCode(
  email: string, 
  type: VerificationCode['type'],
  ipAddress?: string,
  userAgent?: string
): Omit<VerificationCode, '_id'> {
  const now = new Date()
  const expiresAt = new Date(now.getTime() + 15 * 60 * 1000) // 15 minutes
  const code = Math.floor(100000 + Math.random() * 900000).toString() // 6-digit code
  
  return {
    email: email.toLowerCase(),
    code,
    type,
    attempts: 0,
    maxAttempts: 5,
    expiresAt,
    createdAt: now,
    isUsed: false,
    ipAddress,
    userAgent
  }
}

export function createSession(userId: ObjectId, ipAddress?: string, userAgent?: string): Omit<UserSession, '_id'> {
  const now = new Date()
  const expiresAt = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // 30 days
  const sessionToken = generateSessionToken()
  
  return {
    userId,
    sessionToken,
    expiresAt,
    userAgent,
    ipAddress,
    createdAt: now
  }
}

function generateSessionToken(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < 64; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}
