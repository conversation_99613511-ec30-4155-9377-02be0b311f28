"use client"

import type React from "react"
import Link from "next/link"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Menu, X, <PERSON>rkles, Zap } from "lucide-react"

export default function ModernNavbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      window.location.href = `/browse?search=${encodeURIComponent(searchQuery.trim())}`
    }
  }

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
        isScrolled ? "glass-effect shadow-2xl shadow-black/20" : "bg-transparent"
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-500 to-violet-500 rounded-xl blur opacity-75 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative bg-dark-900 p-2 rounded-xl">
                <Sparkles className="h-6 w-6 text-emerald-400" />
              </div>
            </div>
            <span className="text-2xl font-bold gradient-text">AveExplore</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link href="/" className="relative group">
              <span className="text-gray-300 hover:text-white transition-colors duration-300">Home</span>
              <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-emerald-500 to-violet-500 group-hover:w-full transition-all duration-300"></div>
            </Link>
            <Link href="/browse" className="relative group">
              <span className="text-gray-300 hover:text-white transition-colors duration-300">Browse</span>
              <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-emerald-500 to-violet-500 group-hover:w-full transition-all duration-300"></div>
            </Link>

            {/* Modern Search Bar */}
            <form onSubmit={handleSearch} className="relative">
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/20 to-violet-500/20 rounded-2xl blur group-focus-within:blur-md transition-all duration-300"></div>
                <div className="relative flex items-center">
                  <Input
                    type="text"
                    placeholder="Search amazing projects..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-80 input-modern pl-12 pr-4"
                  />
                  <Search className="absolute left-4 h-5 w-5 text-gray-400 group-focus-within:text-emerald-400 transition-colors duration-300" />
                </div>
              </div>
            </form>
          </div>

          {/* Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <Link href="/auth/signin">
              <Button className="btn-ghost">Sign In</Button>
            </Link>
            <Link href="/auth/signup">
              <Button className="btn-primary group">
                <Zap className="h-4 w-4 mr-2 group-hover:animate-pulse" />
                Get Started
              </Button>
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-300 hover:text-white p-2 rounded-xl hover:bg-dark-800/50 transition-all duration-300"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-6 border-t border-dark-700 glass-effect rounded-b-2xl mt-4 animate-slide-up">
            <div className="flex flex-col space-y-6">
              <Link href="/" className="text-gray-300 hover:text-white transition-colors duration-300 px-4">
                Home
              </Link>
              <Link href="/browse" className="text-gray-300 hover:text-white transition-colors duration-300 px-4">
                Browse
              </Link>

              {/* Mobile Search */}
              <form onSubmit={handleSearch} className="px-4">
                <div className="relative group">
                  <Input
                    type="text"
                    placeholder="Search projects..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full input-modern pl-12"
                  />
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                </div>
              </form>

              <div className="flex flex-col space-y-3 px-4 pt-4 border-t border-dark-700">
                <Link href="/auth/signin">
                  <Button className="w-full btn-ghost">Sign In</Button>
                </Link>
                <Link href="/auth/signup">
                  <Button className="w-full btn-primary">
                    <Zap className="h-4 w-4 mr-2" />
                    Get Started
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
