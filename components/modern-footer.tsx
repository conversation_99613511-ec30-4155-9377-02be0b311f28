import Link from "next/link"
import { Spark<PERSON>, Github, Twitter, Mail, Heart, Zap } from "lucide-react"

export default function ModernFooter() {
  return (
    <footer className="relative mt-20 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-t from-dark-950 via-dark-900 to-transparent"></div>
      <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-transparent to-violet-500/5"></div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
          {/* Logo and Description */}
          <div className="col-span-1 md:col-span-2 space-y-6">
            <Link href="/" className="flex items-center space-x-3 group">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-emerald-500 to-violet-500 rounded-xl blur opacity-75 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative bg-dark-900 p-3 rounded-xl">
                  <Sparkles className="h-8 w-8 text-emerald-400" />
                </div>
              </div>
              <span className="text-2xl font-bold gradient-text">AveExplore</span>
            </Link>

            <p className="text-gray-400 max-w-md leading-relaxed">
              Discover and explore incredible open-source projects from talented developers worldwide. Join our
              community and showcase your own amazing creations.
            </p>

            <div className="flex space-x-4">
              {[
                { icon: Github, href: "#", color: "hover:text-white" },
                { icon: Twitter, href: "#", color: "hover:text-blue-400" },
                { icon: Mail, href: "#", color: "hover:text-emerald-400" },
              ].map(({ icon: Icon, href, color }, index) => (
                <a
                  key={index}
                  href={href}
                  className={`text-gray-400 ${color} transition-all duration-300 p-3 rounded-xl hover:bg-dark-800/50 hover:scale-110`}
                >
                  <Icon className="h-5 w-5" />
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-6">
            <h3 className="text-white font-semibold text-lg flex items-center">
              <Zap className="h-5 w-5 mr-2 text-emerald-400" />
              Explore
            </h3>
            <ul className="space-y-3">
              {[
                { name: "Home", href: "/" },
                { name: "Browse Projects", href: "/browse" },
                { name: "Websites", href: "/browse?tag=Website" },
                { name: "Discord Bots", href: "/browse?tag=Discord Bot" },
                { name: "Applications", href: "/browse?tag=Apps" },
              ].map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support */}
          <div className="space-y-6">
            <h3 className="text-white font-semibold text-lg flex items-center">
              <Heart className="h-5 w-5 mr-2 text-rose-400" />
              Community
            </h3>
            <ul className="space-y-3">
              {[
                { name: "Get Started", href: "/auth/signup" },
                { name: "Documentation", href: "#" },
                { name: "Help Center", href: "#" },
                { name: "Contact Us", href: "#" },
                { name: "Report Issue", href: "#" },
              ].map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-dark-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <p className="text-gray-400 text-sm flex items-center">
            © {new Date().getFullYear()} AveExplore. Made with
            <Heart className="h-4 w-4 mx-1 text-rose-400 animate-pulse" />
            for developers
          </p>

          <div className="flex space-x-6">
            {["Privacy Policy", "Terms of Service", "Cookie Policy"].map((link, index) => (
              <a key={index} href="#" className="text-gray-400 hover:text-white text-sm transition-colors duration-300">
                {link}
              </a>
            ))}
          </div>
        </div>
      </div>
    </footer>
  )
}
