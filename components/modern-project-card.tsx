import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Calendar, User, ExternalLink, Star, Eye } from "lucide-react"
import type { Project } from "@/lib/data"

interface ProjectCardProps {
  project: Project
  index?: number
}

export default function ModernProjectCard({ project, index = 0 }: ProjectCardProps) {
  return (
    <div className="group animate-slide-up card-hover" style={{ animationDelay: `${index * 0.1}s` }}>
      <Card className="glass-card border-dark-600 hover:border-emerald-500/30 h-full flex flex-col relative overflow-hidden">
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 via-transparent to-violet-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

        {/* Floating badge for featured projects */}
        {project.featured && (
          <div className="absolute top-4 right-4 z-10">
            <div className="bg-gradient-to-r from-amber-500 to-orange-500 text-white text-xs px-3 py-1 rounded-full font-medium animate-pulse">
              <Star className="h-3 w-3 inline mr-1" />
              Featured
            </div>
          </div>
        )}

        <CardHeader className="relative z-10">
          <CardTitle className="text-white text-xl line-clamp-2 group-hover:text-emerald-400 transition-colors duration-300">
            {project.title}
          </CardTitle>

          <div className="flex items-center space-x-4 text-sm text-gray-400">
            <div className="flex items-center space-x-2 hover:text-emerald-400 transition-colors duration-300">
              <User className="h-4 w-4" />
              <Link href={`/developer/${project.developer.id}`} className="hover:text-white transition-colors">
                {project.developer.name}
              </Link>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span>{new Date(project.lastUpdated).toLocaleDateString()}</span>
            </div>
          </div>
        </CardHeader>

        <CardContent className="flex-1 relative z-10">
          <p className="text-gray-300 text-sm line-clamp-3 mb-6 leading-relaxed">{project.description}</p>

          <div className="flex flex-wrap gap-2">
            {project.tags.map((tag, tagIndex) => (
              <Link key={tag} href={`/browse?tag=${encodeURIComponent(tag)}`}>
                <Badge
                  className={`
                    cursor-pointer transition-all duration-300 hover:scale-105
                    ${tagIndex % 4 === 0 ? "bg-emerald-500/20 text-emerald-400 hover:bg-emerald-500/30" : ""}
                    ${tagIndex % 4 === 1 ? "bg-violet-500/20 text-violet-400 hover:bg-violet-500/30" : ""}
                    ${tagIndex % 4 === 2 ? "bg-amber-500/20 text-amber-400 hover:bg-amber-500/30" : ""}
                    ${tagIndex % 4 === 3 ? "bg-rose-500/20 text-rose-400 hover:bg-rose-500/30" : ""}
                  `}
                >
                  {tag}
                </Badge>
              </Link>
            ))}
          </div>
        </CardContent>

        <CardFooter className="flex space-x-2 relative z-10">
          <Link href={`/project/${project.id}`} className="flex-1">
            <Button className="w-full btn-secondary group">
              <Eye className="h-4 w-4 mr-2 group-hover:text-emerald-400 transition-colors duration-300" />
              View Details
            </Button>
          </Link>
          <a href={project.githubUrl} target="_blank" rel="noopener noreferrer" className="flex-1">
            <Button className="w-full btn-ghost group">
              <ExternalLink className="h-4 w-4 mr-2 group-hover:text-emerald-400 transition-colors duration-300" />
              GitHub
            </Button>
          </a>
        </CardFooter>
      </Card>
    </div>
  )
}
